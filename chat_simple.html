<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAGFlow 聊天测试页面 (简化版)</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            background: #fafafa;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
            max-width: 80%;
        }
        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
        }
        .assistant-message {
            background: white;
            border: 1px solid #ddd;
        }
        .input-area {
            padding: 20px;
            border-top: 1px solid #eee;
            background: white;
        }
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        .input-group input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .input-group button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .input-group button:hover {
            background: #0056b3;
        }
        .input-group button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .config-area {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            font-size: 12px;
        }
        .config-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .debug {
            background: #e9ecef;
            border: 1px solid #ced4da;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>RAGFlow 聊天测试页面 (简化版)</h1>
            <p>贺勇用户Token: a0093ec06cdf11f0a809525400b50619</p>
        </div>
        
        <div class="messages" id="messages"></div>
        
        <div class="input-area">
            <div class="config-area">
                <div class="config-item">
                    <label>Token:</label>
                    <input type="text" id="token" value="a0093ec06cdf11f0a809525400b50619" style="width: 300px;">
                </div>
                <div class="config-item">
                    <label><input type="checkbox" id="stream" checked> 流式</label>
                </div>
                <div class="config-item">
                    <label><input type="checkbox" id="showDebug" checked> 显示调试</label>
                </div>
            </div>
            <div class="input-group">
                <input type="text" id="messageInput" placeholder="输入您的问题..." onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()" id="sendBtn">发送</button>
            </div>
        </div>
    </div>

    <script>
        let conversationId = '';

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function addMessage(role, content) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            messageDiv.innerHTML = content.replace(/\n/g, '<br>');
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            return messageDiv;
        }

        function addDebug(content) {
            if (!document.getElementById('showDebug').checked) return;
            
            const messagesDiv = document.getElementById('messages');
            const debugDiv = document.createElement('div');
            debugDiv.className = 'debug';
            debugDiv.textContent = content;
            messagesDiv.appendChild(debugDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function addError(message) {
            const messagesDiv = document.getElementById('messages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            messagesDiv.appendChild(errorDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const token = document.getElementById('token').value;
            const isStream = document.getElementById('stream').checked;
            const sendBtn = document.getElementById('sendBtn');
            
            const message = input.value.trim();
            if (!message) return;
            
            // 添加用户消息
            addMessage('user', message);
            input.value = '';
            sendBtn.disabled = true;
            
            // 添加加载提示
            const loadingDiv = addMessage('assistant', '正在思考中...');
            loadingDiv.classList.add('loading');
            
            try {
                const requestBody = {
                    conversation_id: conversationId,
                    messages: [{
                        role: 'user',
                        content: message
                    }],
                    stream: isStream,
                    quote: true
                };
                
                addDebug('发送请求: ' + JSON.stringify(requestBody, null, 2));
                
                const response = await fetch('https://jinglueai.cn/rag-api/conversation/completion', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token,
                        'Accept': isStream ? 'text/event-stream' : 'application/json',
                        'Cache-Control': 'no-cache'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                addDebug('响应状态: ' + response.status);
                addDebug('响应头: ' + JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2));
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                if (isStream) {
                    await handleStreamResponse(response);
                } else {
                    await handleJsonResponse(response);
                }
                
                // 移除加载提示
                loadingDiv.remove();
                
            } catch (error) {
                console.error('Error:', error);
                loadingDiv.remove();
                addError('请求失败: ' + error.message);
                addDebug('错误详情: ' + error.stack);
            } finally {
                sendBtn.disabled = false;
            }
        }

        async function handleStreamResponse(response) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';
            let currentAnswerDiv = null;
            
            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    buffer += decoder.decode(value, { stream: true });
                    addDebug('收到数据块: ' + buffer);
                    
                    // 处理 Server-Sent Events 格式
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || ''; // 保留最后一行（可能不完整）
                    
                    for (const line of lines) {
                        addDebug('处理行: ' + line);
                        
                        if (line.startsWith('data: ')) {
                            const dataStr = line.slice(6).trim();
                            
                            if (dataStr === '' || dataStr === '[DONE]') {
                                continue;
                            }
                            
                            try {
                                const data = JSON.parse(dataStr);
                                addDebug('解析的JSON: ' + JSON.stringify(data, null, 2));

                                // 强制检查所有可能的答案字段
                                let answer = null;
                                if (data && data.data && data.data.answer) {
                                    answer = data.data.answer;
                                    addDebug('找到答案在 data.data.answer: ' + answer);
                                } else if (data && data.message && data.message !== '') {
                                    answer = data.message;
                                    addDebug('找到答案在 data.message: ' + answer);
                                } else if (data && data.answer) {
                                    answer = data.answer;
                                    addDebug('找到答案在 data.answer: ' + answer);
                                }

                                // 强制显示答案
                                if (answer && answer.trim() !== '') {
                                    addDebug('准备显示答案: ' + answer);

                                    if (!currentAnswerDiv) {
                                        addDebug('创建新的答案div');
                                        currentAnswerDiv = addMessage('assistant', answer);
                                    } else {
                                        addDebug('更新现有答案div');
                                        currentAnswerDiv.innerHTML = answer.replace(/\n/g, '<br>');
                                    }

                                    // 强制滚动到底部
                                    const messagesDiv = document.getElementById('messages');
                                    messagesDiv.scrollTop = messagesDiv.scrollHeight;

                                    // 更新对话ID
                                    if (data.data && data.data.session_id) {
                                        conversationId = data.data.session_id;
                                        addDebug('更新对话ID: ' + conversationId);
                                    }
                                } else {
                                    addDebug('没有找到有效答案，完整数据: ' + JSON.stringify(data, null, 2));
                                }
                            } catch (e) {
                                addDebug('JSON解析错误: ' + e.message + ', 原始数据: ' + dataStr);
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }
        }

        async function handleJsonResponse(response) {
            const data = await response.json();
            addDebug('JSON响应: ' + JSON.stringify(data, null, 2));

            // 强制检查所有可能的答案字段
            let answer = null;
            if (data && data.data && data.data.answer) {
                answer = data.data.answer;
                addDebug('非流式找到答案在 data.data.answer: ' + answer);
            } else if (data && data.message && data.message !== '') {
                answer = data.message;
                addDebug('非流式找到答案在 data.message: ' + answer);
            } else if (data && data.answer) {
                answer = data.answer;
                addDebug('非流式找到答案在 data.answer: ' + answer);
            }

            if (answer && answer.trim() !== '') {
                addMessage('assistant', answer);

                // 更新对话ID
                if (data.data && data.data.session_id) {
                    conversationId = data.data.session_id;
                    addDebug('非流式更新对话ID: ' + conversationId);
                }
            } else {
                addError('未找到有效答案，完整数据: ' + JSON.stringify(data, null, 2));
            }
        }

        function extractAnswer(data) {
            // 尝试多种可能的数据结构
            console.log('extractAnswer 输入:', data);

            if (data && data.data && data.data.answer) {
                console.log('找到 data.data.answer:', data.data.answer);
                return data.data.answer;
            }
            if (data && data.message && data.message !== '') {
                console.log('找到 data.message:', data.message);
                return data.message;
            }
            if (data && data.answer) {
                console.log('找到 data.answer:', data.answer);
                return data.answer;
            }
            if (typeof data === 'string') {
                console.log('数据是字符串:', data);
                return data;
            }

            console.log('未找到答案，数据结构:', data);
            return null;
        }
    </script>
</body>
</html>
