#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通义千问表格识别模块
用于替换本地表格识别模型，提高表格解析速度
"""

import base64
import requests
import logging
from typing import List, Dict, Any
from PIL import Image
import io
import json

from api.utils import get_base_config

logger = logging.getLogger(__name__)


class QwenTableRecognizer:
    """通义千问表格识别器"""
    
    def __init__(self):
        """初始化通义千问表格识别器"""
        self.config = get_base_config("qwen_ocr", {})
        self.enabled = self.config.get("enabled", False)
        self.model = "qwen-vl-plus"  # 使用VL模型进行表格识别
        self.api_key = self.config.get("api_key", "")
        self.base_url = self.config.get("base_url", "https://dashscope.aliyuncs.com/compatible-mode/v1")
        
        # 表格识别标签，与原本TableStructureRecognizer保持一致
        self.labels = [
            "table",
            "table column",
            "table row", 
            "table column header",
            "table projected row header",
            "table spanning cell",
        ]
        
        if not self.api_key:
            logger.warning("通义千问表格识别API密钥未配置")
            self.enabled = False
    
    def is_enabled(self) -> bool:
        """检查是否启用通义千问表格识别"""
        return self.enabled and bool(self.api_key)
    
    def recognize_table(self, image_data: bytes) -> str:
        """
        使用通义千问识别表格结构并返回HTML
        
        Args:
            image_data: 图像二进制数据
        
        Returns:
            表格的HTML表示
        """
        if not self.is_enabled():
            logger.warning("通义千问表格识别未启用，返回空结果")
            return ""
        
        try:
            # 将图像转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 构建请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 表格识别提示词
            prompt = """请识别图像中的表格并转换为HTML格式。

输出要求：
1. 使用标准的HTML表格标签 <table>, <tr>, <td>, <th>
2. 保持表格的原始结构和内容
3. 对于表头使用 <th> 标签
4. 对于普通单元格使用 <td> 标签
5. 如果有合并单元格，使用 colspan 或 rowspan 属性
6. 只输出HTML表格代码，不要任何额外说明
7. 确保HTML格式正确可解析

示例格式：
<table>
<tr><th>列1</th><th>列2</th><th>列3</th></tr>
<tr><td>数据1</td><td>数据2</td><td>数据3</td></tr>
<tr><td>数据4</td><td>数据5</td><td>数据6</td></tr>
</table>

请开始识别表格："""
            
            data = {
                "model": self.model,
                "messages": [{
                    "role": "user",
                    "content": [{
                        "type": "image_url",
                        "image_url": f"data:image/jpeg;base64,{image_base64}",
                        "min_pixels": 28 * 28 * 4,
                        "max_pixels": 28 * 28 * 8192
                    }, {
                        "type": "text",
                        "text": prompt
                    }]
                }]
            }
            
            # 发送请求
            url = f"{self.base_url}/chat/completions"
            response = requests.post(url, headers=headers, json=data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # 记录token使用情况
                usage = result.get('usage', {})
                logger.info(f"通义千问表格识别调用成功，使用Token: {usage.get('total_tokens', 0)}")
                
                # 清理HTML内容
                html_content = content.strip()
                if html_content.startswith('```html'):
                    html_content = html_content[7:]
                if html_content.endswith('```'):
                    html_content = html_content[:-3]
                
                return html_content.strip()
            else:
                logger.error(f"通义千问表格识别调用失败: {response.status_code} - {response.text}")
                return ""
                
        except Exception as e:
            logger.error(f"通义千问表格识别异常: {str(e)}")
            return ""
    
    def extract_table_structure(self, image_data: bytes) -> List[Dict[str, Any]]:
        """
        提取表格结构信息，返回与原本TableStructureRecognizer一致的格式
        
        Args:
            image_data: 图像二进制数据
        
        Returns:
            表格结构信息列表
        """
        if not self.is_enabled():
            logger.warning("通义千问表格识别未启用，返回空结果")
            return []
        
        try:
            # 将图像转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 构建请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 表格结构分析提示词
            prompt = """请分析图像中的表格结构，并按照以下JSON格式输出：

{
    "table_regions": [
        {
            "type": "table",
            "bbox": [x1, y1, x2, y2],
            "confidence": 0.95
        },
        {
            "type": "table column",
            "bbox": [x1, y1, x2, y2],
            "confidence": 0.90
        },
        {
            "type": "table row",
            "bbox": [x1, y1, x2, y2],
            "confidence": 0.90
        }
    ]
}

输出要求：
1. bbox坐标为相对坐标，范围0-1
2. type必须是以下之一：table, table column, table row, table column header, table projected row header, table spanning cell
3. confidence为置信度，范围0-1
4. 只输出JSON格式，不要任何额外说明
5. 确保JSON格式正确可解析

请开始分析表格结构："""
            
            data = {
                "model": self.model,
                "messages": [{
                    "role": "user",
                    "content": [{
                        "type": "image_url",
                        "image_url": f"data:image/jpeg;base64,{image_base64}",
                        "min_pixels": 28 * 28 * 4,
                        "max_pixels": 28 * 28 * 8192
                    }, {
                        "type": "text",
                        "text": prompt
                    }]
                }]
            }
            
            # 发送请求
            url = f"{self.base_url}/chat/completions"
            response = requests.post(url, headers=headers, json=data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # 记录token使用情况
                usage = result.get('usage', {})
                logger.info(f"通义千问表格结构分析调用成功，使用Token: {usage.get('total_tokens', 0)}")
                
                # 解析JSON结果
                try:
                    structure_data = json.loads(content)
                    table_regions = structure_data.get("table_regions", [])
                    
                    # 转换为与原本格式一致的结构
                    results = []
                    for region in table_regions:
                        bbox = region.get("bbox", [0, 0, 1, 1])
                        results.append({
                            "type": region.get("type", "table"),
                            "bbox": bbox,
                            "score": region.get("confidence", 0.9),
                            "label": region.get("type", "table")
                        })
                    
                    return results
                    
                except json.JSONDecodeError:
                    logger.warning("通义千问表格结构分析返回的不是有效JSON，返回默认结构")
                    return [{
                        "type": "table",
                        "bbox": [0, 0, 1, 1],
                        "score": 0.9,
                        "label": "table"
                    }]
            else:
                logger.error(f"通义千问表格结构分析调用失败: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"通义千问表格结构分析异常: {str(e)}")
            return []
    
    def __call__(self, image_list: List, thr: float = 0.7, batch_size: int = 16) -> List[List[Dict[str, Any]]]:
        """
        兼容原始TableStructureRecognizer接口
        
        Args:
            image_list: 图像列表
            thr: 阈值
            batch_size: 批处理大小
        
        Returns:
            表格结构识别结果列表
        """
        results = []
        
        for i, img in enumerate(image_list):
            try:
                # 将图像转换为字节数据
                if hasattr(img, 'save'):
                    # PIL图像
                    buffer = io.BytesIO()
                    img.save(buffer, format='JPEG', quality=95)
                    image_data = buffer.getvalue()
                else:
                    # numpy数组，转换为PIL图像
                    pil_img = Image.fromarray(img)
                    buffer = io.BytesIO()
                    pil_img.save(buffer, format='JPEG', quality=95)
                    image_data = buffer.getvalue()
                
                # 提取表格结构
                structure_result = self.extract_table_structure(image_data)
                
                # 过滤低置信度结果
                filtered_result = [r for r in structure_result if r.get("score", 0) >= thr]
                
                results.append(filtered_result)
                
            except Exception as e:
                logger.error(f"处理第{i}个图像时出错: {str(e)}")
                results.append([])
        
        return results

    def construct_table(self, bxs: List[Dict], html: bool = True, is_english: bool = True) -> str:
        """
        构建表格，兼容原始TableStructureRecognizer接口

        Args:
            bxs: 文本框列表
            html: 是否返回HTML格式
            is_english: 是否为英文

        Returns:
            表格的HTML或文本表示
        """
        if not self.is_enabled():
            # 如果未启用，返回简单的表格格式
            if not bxs:
                return ""

            if html:
                # 简单的HTML表格
                rows = []
                current_row = []
                current_top = None

                # 按位置排序文本框
                sorted_bxs = sorted(bxs, key=lambda x: (x.get("top", 0), x.get("x0", 0)))

                for box in sorted_bxs:
                    text = box.get("text", "").strip()
                    if not text:
                        continue

                    box_top = box.get("top", 0)

                    # 判断是否为新行
                    if current_top is None or abs(box_top - current_top) > 10:
                        if current_row:
                            rows.append(current_row)
                        current_row = [text]
                        current_top = box_top
                    else:
                        current_row.append(text)

                if current_row:
                    rows.append(current_row)

                # 构建HTML表格
                if rows:
                    html_rows = []
                    for i, row in enumerate(rows):
                        if i == 0:
                            # 第一行作为表头
                            cells = "".join([f"<th>{cell}</th>" for cell in row])
                            html_rows.append(f"<tr>{cells}</tr>")
                        else:
                            cells = "".join([f"<td>{cell}</td>" for cell in row])
                            html_rows.append(f"<tr>{cells}</tr>")

                    return f"<table>{''.join(html_rows)}</table>"
                else:
                    return "<table></table>"
            else:
                # 文本格式
                text_parts = []
                for box in sorted(bxs, key=lambda x: (x.get("top", 0), x.get("x0", 0))):
                    text = box.get("text", "").strip()
                    if text:
                        text_parts.append(text)
                return " | ".join(text_parts)

        # 使用通义千问进行表格识别
        try:
            # 这里需要图像数据，但是bxs只包含文本框信息
            # 作为简化处理，我们基于文本框构建表格
            if not bxs:
                return ""

            if html:
                # 基于文本框位置构建HTML表格
                rows = []
                current_row = []
                current_top = None

                # 按位置排序文本框
                sorted_bxs = sorted(bxs, key=lambda x: (x.get("top", 0), x.get("x0", 0)))

                for box in sorted_bxs:
                    text = box.get("text", "").strip()
                    if not text:
                        continue

                    box_top = box.get("top", 0)

                    # 判断是否为新行（容差为平均高度的一半）
                    if current_top is None or abs(box_top - current_top) > 15:
                        if current_row:
                            rows.append(current_row)
                        current_row = [text]
                        current_top = box_top
                    else:
                        current_row.append(text)

                if current_row:
                    rows.append(current_row)

                # 构建HTML表格
                if rows:
                    html_rows = []
                    max_cols = max(len(row) for row in rows) if rows else 0

                    for i, row in enumerate(rows):
                        # 补齐列数
                        while len(row) < max_cols:
                            row.append("")

                        if i == 0:
                            # 第一行作为表头
                            cells = "".join([f"<th>{cell}</th>" for cell in row])
                            html_rows.append(f"<tr>{cells}</tr>")
                        else:
                            cells = "".join([f"<td>{cell}</td>" for cell in row])
                            html_rows.append(f"<tr>{cells}</tr>")

                    return f"<table>{''.join(html_rows)}</table>"
                else:
                    return "<table></table>"
            else:
                # 文本格式
                text_parts = []
                for box in sorted(bxs, key=lambda x: (x.get("top", 0), x.get("x0", 0))):
                    text = box.get("text", "").strip()
                    if text:
                        text_parts.append(text)
                return " | ".join(text_parts)

        except Exception as e:
            logger.error(f"通义千问表格构建异常: {str(e)}")
            return ""


# 全局实例
qwen_table = QwenTableRecognizer()


def get_qwen_table() -> QwenTableRecognizer:
    """获取通义千问表格识别器实例"""
    return qwen_table
