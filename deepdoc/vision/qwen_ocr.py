#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通义千问OCR集成模块
用于替换本地OCR模型，提高PDF解析速度
"""

import base64
import requests
import logging
from typing import List, Tuple, Optional
from PIL import Image
import io
import numpy as np

from api.utils import get_base_config

logger = logging.getLogger(__name__)


class QwenOCR:
    """通义千问OCR客户端"""
    
    def __init__(self):
        """初始化通义千问OCR客户端"""
        self.config = get_base_config("qwen_ocr", {})
        self.enabled = self.config.get("enabled", False)
        self.model = self.config.get("model", "qwen-vl-ocr-latest")
        self.api_key = self.config.get("api_key", "")
        self.base_url = self.config.get("base_url", "https://dashscope.aliyuncs.com/compatible-mode/v1")
        
        if not self.api_key:
            logger.warning("通义千问OCR API密钥未配置")
            self.enabled = False
    
    def is_enabled(self) -> bool:
        """检查是否启用通义千问OCR"""
        return self.enabled and bool(self.api_key)
    
    def image_to_base64(self, image_data: bytes) -> str:
        """将图像数据转换为base64编码"""
        return base64.b64encode(image_data).decode('utf-8')
    
    def pil_to_base64(self, pil_image: Image.Image) -> str:
        """将PIL图像转换为base64编码"""
        buffer = io.BytesIO()
        pil_image.save(buffer, format='JPEG', quality=95)
        image_data = buffer.getvalue()
        return self.image_to_base64(image_data)
    
    def extract_text(self, image_data: bytes, task_type: str = "document_parsing") -> str:
        """
        使用通义千问OCR提取图像中的文字

        Args:
            image_data: 图像二进制数据
            task_type: OCR任务类型 (document_parsing, table_parsing, formula_recognition, key_information_extraction, multi_lan)

        Returns:
            提取的文字内容
        """
        if not self.is_enabled():
            logger.warning("通义千问OCR未启用，跳过OCR处理")
            return ""

        try:
            # 将图像转换为base64
            image_base64 = self.image_to_base64(image_data)

            # 构建请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # 根据任务类型设置详细的提示词和返回格式
            if task_type == "document_parsing":
                prompt = """请按照以下格式提取文档中的所有文字内容：

输出格式要求：
1. 按照从上到下、从左到右的阅读顺序输出文字
2. 保持原文的段落结构和换行
3. 对于标题，在前面加上【标题】标记
4. 对于正文段落，直接输出内容
5. 对于列表项，在前面加上"• "
6. 不要添加任何解释性文字，只输出提取的文本内容
7. 保持原文的语言（中文/英文等）

请开始提取文档内容："""

            elif task_type == "structured_ocr":
                # 专门为PDF解析设计的结构化OCR
                prompt = """请提取图像中的所有文字内容，并按照以下JSON格式输出：

{
    "text_blocks": [
        {
            "text": "提取的文字内容",
            "position": "relative",
            "confidence": 0.95
        }
    ]
}

输出要求：
1. 按照从上到下、从左到右的阅读顺序提取文字
2. 每个文字块包含完整的句子或段落
3. 保持原文的语言和格式
4. 不要添加任何解释，只输出JSON格式的结果
5. 确保JSON格式正确可解析

请开始提取："""

            elif task_type == "table_parsing":
                prompt = """请按照以下格式提取表格内容：

输出格式要求：
1. 使用 | 分隔列
2. 使用换行分隔行
3. 第一行为表头（如果有）
4. 保持表格的原始结构
5. 空单元格用 - 表示
6. 数字保持原格式

示例格式：
列1 | 列2 | 列3
数据1 | 数据2 | 数据3
数据4 | 数据5 | 数据6

请开始提取表格："""

            elif task_type == "formula_recognition":
                prompt = """Please extract and output the mathematical formulas from this image.
Output in LaTeX format if possible, otherwise in plain text mathematical notation."""

            elif task_type == "key_information_extraction":
                # 设置结构化信息抽取格式
                result_schema = """{
    "标题": "",
    "主要内容": "",
    "关键信息": [],
    "数字信息": [],
    "日期信息": [],
    "联系方式": []
}"""
                prompt = f"""请作为信息提取专家，根据给定的JSON格式提取图像中的关键信息。

输出格式要求：
1. 严格按照以下JSON格式输出
2. 如果某个字段没有对应信息，保持为空字符串或空数组
3. 数组字段如果有多个值，请全部列出
4. 输出语言与图像中的文字保持一致
5. 只输出JSON，不要任何额外说明
6. 确保JSON格式正确，可以被解析

JSON格式模板：
{result_schema}

请开始提取："""

            elif task_type == "multi_lan":
                prompt = """Please extract all text content from this image, supporting multiple languages.
Maintain the original language of the text. Output in a clean format without additional descriptions."""

            else:
                # 默认文本识别
                prompt = """请提取图像中的所有文字内容：

输出格式要求：
1. 按照从上到下、从左到右的顺序输出
2. 保持原文的换行和段落结构
3. 不要添加任何标记或解释
4. 只输出纯文本内容
5. 如果有多种语言，保持原语言输出
6. 忽略图像中的噪点和装饰元素

请开始提取："""

            # 构建请求数据，完全按照官方示例格式
            data = {
                "model": self.model,
                "messages": [{
                    "role": "user",
                    "content": [{
                        "type": "image_url",
                        "image_url": f"data:image/jpeg;base64,{image_base64}",
                        "min_pixels": 28 * 28 * 4,
                        "max_pixels": 28 * 28 * 8192
                    }, {
                        "type": "text",
                        "text": prompt
                    }]
                }]
            }

            # 使用OpenAI兼容API
            url = f"{self.base_url}/chat/completions"

            # 发送请求
            response = requests.post(url, headers=headers, json=data, timeout=120)  # 增加超时时间到120秒

            if response.status_code == 200:
                result = response.json()

                # 解析响应
                content = result['choices'][0]['message']['content']
                usage = result.get('usage', {})

                # 记录token使用情况
                logger.info(f"通义千问OCR调用成功，任务类型: {task_type}, 使用Token: {usage.get('total_tokens', 0)}")

                return content.strip()
            else:
                logger.error(f"通义千问OCR调用失败: {response.status_code} - {response.text}")
                return ""

        except Exception as e:
            logger.error(f"通义千问OCR处理异常: {str(e)}")
            return ""

    def extract_structured_text(self, image_data: bytes, img_size: tuple, zoom_factor: int, page_num: int) -> list:
        """
        为PDF解析器提供结构化文字提取，返回与原本OCR一致的格式

        Args:
            image_data: 图像二进制数据
            img_size: 图像尺寸 (width, height)
            zoom_factor: 缩放因子
            page_num: 页码

        Returns:
            与原本OCR格式一致的文本框列表
        """
        if not self.is_enabled():
            logger.warning("通义千问OCR未启用，返回空结果")
            return []

        try:
            # 调用结构化OCR
            ocr_text = self.extract_text(image_data, task_type="structured_ocr")

            if not ocr_text:
                return []

            # 尝试解析JSON格式的结果
            import json
            try:
                result = json.loads(ocr_text)
                text_blocks = result.get("text_blocks", [])
            except:
                # 如果不是JSON格式，作为纯文本处理
                text_blocks = [{"text": ocr_text.strip(), "position": "full", "confidence": 0.9}]

            # 转换为与原本OCR一致的格式
            img_width, img_height = img_size
            bxs = []

            # 如果只有一个文本块，覆盖整个页面
            if len(text_blocks) == 1:
                text_content = text_blocks[0]["text"]
                if text_content:
                    bxs.append({
                        "x0": 0,
                        "x1": img_width / zoom_factor,
                        "top": 0,
                        "bottom": img_height / zoom_factor,
                        "text": text_content,
                        "txt": text_content,
                        "page_number": page_num,
                        "layout_type": "text"  # 添加布局类型字段
                    })
            else:
                # 多个文本块，按垂直位置分布
                for i, block in enumerate(text_blocks):
                    text_content = block.get("text", "").strip()
                    if text_content:
                        # 简单的垂直分布
                        block_height = (img_height / zoom_factor) / len(text_blocks)
                        top = i * block_height
                        bottom = (i + 1) * block_height

                        bxs.append({
                            "x0": 0,
                            "x1": img_width / zoom_factor,
                            "top": top,
                            "bottom": bottom,
                            "text": text_content,
                            "txt": text_content,
                            "page_number": page_num,
                            "layout_type": "text"  # 添加布局类型字段
                        })

            return bxs

        except Exception as e:
            logger.error(f"通义千问结构化OCR处理异常: {str(e)}")
            return []

    def extract_text_from_pil(self, pil_image: Image.Image, task_type: str = "general") -> str:
        """
        从PIL图像提取文字
        
        Args:
            pil_image: PIL图像对象
            task_type: OCR任务类型
        
        Returns:
            提取的文字内容
        """
        # 将PIL图像转换为字节数据
        buffer = io.BytesIO()
        pil_image.save(buffer, format='JPEG', quality=95)
        image_data = buffer.getvalue()
        
        return self.extract_text(image_data, task_type)
    
    def batch_extract_text(self, image_list: List[bytes], task_type: str = "general") -> List[str]:
        """
        批量提取多个图像的文字
        
        Args:
            image_list: 图像数据列表
            task_type: OCR任务类型
        
        Returns:
            提取的文字内容列表
        """
        results = []
        for i, image_data in enumerate(image_list):
            logger.info(f"处理第 {i+1}/{len(image_list)} 个图像")
            text = self.extract_text(image_data, task_type)
            results.append(text)
        
        return results


# 全局实例
qwen_ocr = QwenOCR()


def get_qwen_ocr() -> QwenOCR:
    """获取通义千问OCR实例"""
    return qwen_ocr
