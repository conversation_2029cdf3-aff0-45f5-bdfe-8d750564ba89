#!/bin/bash

# RAGFlow 启动脚本
# 作者: AI Assistant
# 日期: 2025-07-02

echo "=== RAGFlow 启动脚本 ==="
echo "启动时间: $(date)"

# 设置工作目录
RAGFLOW_DIR="/data/ragflow"
cd "$RAGFLOW_DIR" || {
    echo "错误: 无法进入目录 $RAGFLOW_DIR"
    exit 1
}

echo "当前工作目录: $(pwd)"

# 设置环境变量
export PYTHONPATH="$RAGFLOW_DIR:$PYTHONPATH"
export STORAGE_IMPL="AWS_S3"  # 使用S3存储（腾讯云COS），节省内存
export DOC_ENGINE="elasticsearch"  # 使用Elasticsearch作为文档引擎
export NLTK_DATA="$RAGFLOW_DIR/nltk_data"  # 设置NLTK数据路径

echo "环境变量设置:"
echo "  PYTHONPATH=$PYTHONPATH"
echo "  STORAGE_IMPL=$STORAGE_IMPL (腾讯云COS，节省内存)"
echo "  DOC_ENGINE=$DOC_ENGINE"
echo "  NLTK_DATA=$NLTK_DATA"

# 检查Python3是否可用
if ! command -v python3 &> /dev/null; then
    echo "错误: python3 未找到，请先安装Python3"
    exit 1
fi

echo "Python版本: $(python3 --version)"

# 检查必要的文件
if [ ! -f "api/ragflow_server.py" ]; then
    echo "错误: 找不到 api/ragflow_server.py"
    exit 1
fi

if [ ! -f "conf/service_conf.yaml" ]; then
    echo "错误: 找不到配置文件 conf/service_conf.yaml"
    exit 1
fi

# 检查前端构建文件
if [ ! -d "web/dist" ]; then
    echo "警告: 前端构建文件不存在，请先构建前端"
    echo "运行: cd web && npm run build"
    exit 1
fi

echo "前端文件检查通过"

# 创建日志目录
mkdir -p logs

echo "=== 启动RAGFlow服务器 ==="
echo "服务将在 http://0.0.0.0:9380 启动"
echo "按 Ctrl+C 停止服务"
echo ""

# 启动服务器
python3 api/ragflow_server.py

echo ""
echo "=== RAGFlow 服务已停止 ==="
echo "停止时间: $(date)"
