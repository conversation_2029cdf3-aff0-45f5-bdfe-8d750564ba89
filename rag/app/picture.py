#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import io
import re
import logging

import numpy as np
from PIL import Image

from api.db import LLMType
from api.db.services.llm_service import LLMBundle
from api.utils.image_utils import compress_image, optimize_image_for_vision_model
from deepdoc.vision import OCR
from rag.nlp import tokenize
from rag.utils import clean_markdown_block
from rag.nlp import rag_tokenizer

# Try to initialize OCR, but handle failure gracefully
try:
    ocr = OCR()
except Exception as e:
    print(f"Warning: OCR initialization failed: {e}")
    print("RAGFlow will continue without OCR functionality")
    ocr = None


def chunk(filename, binary, tenant_id, lang, callback=None, **kwargs):
    try:
        # 压缩图片以提高处理速度
        callback(0.1, "Compressing image...")
        compressed_binary = compress_image(binary, max_size_mb=50, quality=85)

        img = Image.open(io.BytesIO(compressed_binary)).convert('RGB')
        doc = {
            "docnm_kwd": filename,
            "title_tks": rag_tokenizer.tokenize(re.sub(r"\.[a-zA-Z]+$", "", filename)),
            "image": img,
            "doc_type_kwd": "image"
        }

        # OCR文字提取
        callback(0.2, "Extracting text with OCR...")
        if ocr is not None:
            bxs = ocr(np.array(img))
            txt = "\n".join([t[0] for _, t in bxs if t[0]])
        else:
            txt = ""
            logging.warning("OCR not available, skipping text extraction")

        eng = lang.lower() == "english"
        callback(0.4, "Finish OCR: (%s ...)" % txt[:12])

        # 如果OCR提取的文字足够多，直接使用OCR结果
        if (eng and len(txt.split()) > 32) or len(txt) > 32:
            tokenize(doc, txt, eng)
            callback(0.8, "OCR results is sufficient, skipping CV LLM.")
            return [doc]

        # 使用视觉模型进行图片理解
        try:
            callback(0.5, "Using vision model to analyze image...")
            cv_mdl = LLMBundle(tenant_id, LLMType.IMAGE2TEXT, lang=lang)

            # 为视觉模型优化图片
            optimized_binary = optimize_image_for_vision_model(compressed_binary, target_size_mb=10)

            # 调用视觉模型
            ans, token_count = cv_mdl.describe(optimized_binary)
            callback(0.8, "Vision model response: %s ..." % ans[:32])

            # 合并OCR和视觉模型结果
            if txt.strip():
                combined_txt = f"OCR提取的文字:\n{txt}\n\n图片描述:\n{ans}"
            else:
                combined_txt = ans

            tokenize(doc, combined_txt, eng)
            logging.info(f"Image analysis completed. OCR: {len(txt)} chars, Vision: {len(ans)} chars, Tokens: {token_count}")
            return [doc]

        except Exception as e:
            logging.error(f"Vision model failed: {str(e)}")
            callback(prog=-1, msg=f"Vision model error: {str(e)}")

            # 如果视觉模型失败，至少返回OCR结果
            if txt.strip():
                tokenize(doc, txt, eng)
                return [doc]

    except Exception as e:
        logging.error(f"Image processing failed: {str(e)}")
        callback(prog=-1, msg=str(e))

    return []


def vision_llm_chunk(binary, vision_model, prompt=None, callback=None):
    """
    A simple wrapper to process image to markdown texts via VLM.

    Returns:
        Simple markdown texts generated by VLM.
    """
    callback = callback or (lambda prog, msg: None)

    img = binary
    txt = ""

    try:
        img_binary = io.BytesIO()
        img.save(img_binary, format='JPEG')
        img_binary.seek(0)

        ans = clean_markdown_block(vision_model.describe_with_prompt(img_binary.read(), prompt))

        txt += "\n" + ans

        return txt

    except Exception as e:
        callback(-1, str(e))

    return ""
