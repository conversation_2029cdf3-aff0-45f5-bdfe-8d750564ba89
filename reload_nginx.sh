#!/bin/bash

#
# Nginx 重载配置脚本
#

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

# 检查nginx配置
check_nginx_config() {
    echo "检查nginx配置文件..."

    # 测试nginx配置
    if docker exec jlwl-nginx nginx -t; then
        success "nginx配置文件语法正确"
        return 0
    else
        error "nginx配置文件语法错误"
        return 1
    fi
}

# 重载nginx配置
reload_nginx() {
    echo "重载nginx配置..."

    if docker exec jlwl-nginx nginx -s reload; then
        success "nginx配置重载成功"
        return 0
    else
        error "nginx配置重载失败"
        return 1
    fi
}

# 检查nginx状态
check_nginx_status() {
    echo "检查nginx服务状态..."

    if docker ps | grep -q jlwl-nginx; then
        success "nginx容器正在运行"
        return 0
    else
        error "nginx容器未运行"
        return 1
    fi
}

# 显示配置信息
show_config_info() {
    echo ""
    echo "=== RAG API 配置信息 ==="
    echo "域名: jinglueai.cn"
    echo "API接口: https://jinglueai.cn/rag-api/"
    echo "API文档: https://jinglueai.cn/rag-apidocs"
    echo ""
    echo "后端服务: http://172.30.0.9:9380"
    echo "配置文件: /data/nginx/conf/conf.d/jinglueai.cn.conf"
    echo ""
}

# 主函数
main() {
    echo "=== Nginx RAG API 配置重载 ==="
    
    # 检查配置
    if ! check_nginx_config; then
        exit 1
    fi
    
    # 重载配置
    if ! reload_nginx; then
        exit 1
    fi
    
    # 检查状态
    check_nginx_status
    
    # 显示配置信息
    show_config_info
    
    success "nginx配置重载完成！"
}

# 执行主函数
main "$@"
