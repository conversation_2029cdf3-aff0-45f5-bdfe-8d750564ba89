{"id": 7, "title": "Medical consultation", "description": "A consultant that offers medical suggestions using an internal QA dataset and PubMed search results. Note that this agent's answers are for reference only and may not be valid. The dataset can be found at https://huggingface.co/datasets/InfiniFlow/medical_QA/tree/main", "canvas_type": "chatbot", "dsl": {"answer": [], "components": {"Answer:FlatRavensPush": {"downstream": ["Generate:QuietMelonsHear", "Generate:FortyBaboonsRule"], "obj": {"component_name": "Answer", "inputs": [], "output": null, "params": {"debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "post_answers": [], "query": []}}, "upstream": ["begin", "Generate:BrightCitiesSink"]}, "Generate:BrightCitiesSink": {"downstream": ["Answer:FlatRavensPush"], "obj": {"component_name": "Generate", "inputs": [], "output": null, "params": {"cite": true, "debug_inputs": [], "frequency_penalty": 0.7, "inputs": [], "llm_id": "deepseek-chat@DeepSeek", "max_tokens": 0, "message_history_window_size": 12, "output": null, "output_var_name": "output", "parameters": [], "presence_penalty": 0.4, "prompt": "Role: You are a professional medical consulting assistant\n\nTasks: Answer questions posed by users. Answer based on content provided by the knowledge base, PubMed\n\nRequirement:\n- Answers may refer to the content provided (Knowledge Base, PubMed).\n- If the provided PubMed content is referenced, a link to the corresponding URL should be given.\n-Answers should be professional and accurate; no information should be fabricated that is not relevant to the user's question.\n\nProvided knowledge base content\n{Retrieval:BeigeBagsDress}\n\nPubMed content provided\n\n{PubMed:TwentyFansShake}", "query": [], "temperature": 0.1, "top_p": 0.3}}, "upstream": ["Retrieval:BeigeBagsDress", "PubMed:TwentyFansShake"]}, "Generate:FortyBaboonsRule": {"downstream": ["PubMed:TwentyFansShake"], "obj": {"component_name": "Generate", "inputs": [], "output": null, "params": {"cite": false, "debug_inputs": [], "frequency_penalty": 0.7, "inputs": [], "llm_id": "deepseek-chat@DeepSeek", "max_tokens": 256, "message_history_window_size": 1, "output": null, "output_var_name": "output", "parameters": [], "presence_penalty": 0.4, "prompt": "Role: You are a professional Chinese-English medical question translation assistant\n\nTask: Accurately translate users' Chinese medical question content into English, ensuring accuracy of terminology and clarity of expression\n\nRequirements:\n- In-depth understanding of the terminology and disease descriptions in Chinese medical inquiries to ensure correct medical vocabulary is used in the English translation.\n- Maintain the semantic integrity and accuracy of the original text to avoid omitting important information or introducing errors.\n- Pay attention to the differences in expression habits between Chinese and English, and make appropriate adjustments to make the English translation more natural and fluent.\n- Respect the patient's privacy and the principle of medical confidentiality, and do not disclose any sensitive information during the translation process.\n\nExample：\nOriginal sentence: 我最近总是感觉胸闷，有时还会有心悸的感觉。\nTranslated: I've been feeling chest tightness recently, and sometimes I experience palpitations.\n\nNote:\nOnly the translated content should be given, do not output other irrelevant content!", "query": [], "temperature": 0.1, "top_p": 0.3}}, "upstream": ["Answer:FlatRavensPush"]}, "Generate:QuietMelonsHear": {"downstream": ["Retrieval:BeigeBagsDress"], "obj": {"component_name": "Generate", "inputs": [], "output": null, "params": {"cite": true, "debug_inputs": [], "frequency_penalty": 0.7, "inputs": [], "llm_id": "deepseek-chat@DeepSeek", "max_tokens": 256, "message_history_window_size": 12, "output": null, "output_var_name": "output", "parameters": [], "presence_penalty": 0.4, "prompt": "Role: You are a professional medical consulting translation assistant\n\nTask: Translate user questions into Chinese, ensuring accuracy of medical terminology and appropriateness of context.\n\nRequirements:\n- Accurately translate medical terminology to convey the integrity and emotional color of the original message.\n- For unclear or uncertain medical terminology, the original text may be retained to ensure accuracy.\n- Respect the privacy and sensitivity of medical consultations and ensure that sensitive information is not disclosed during the translation process.\n- If the user's question is in Chinese, there is no need to translate, just output the user's question directly\n\nExample:\nOriginal (English): Doctor, I have been suffering from chest pain and shortness of breath for the past few days.\nTranslation (Chinese): 医生，我这几天一直胸痛和气短。\n\nNote:\nOnly the translated content needs to be output, no other irrelevant content!", "query": [], "temperature": 0.1, "top_p": 0.3}}, "upstream": ["Answer:FlatRavensPush"]}, "PubMed:TwentyFansShake": {"downstream": ["Generate:BrightCitiesSink"], "obj": {"component_name": "PubMed", "inputs": [], "output": null, "params": {"debug_inputs": [], "email": "<EMAIL>", "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "query": [{"component_id": "Generate:FortyBaboonsRule", "type": "reference"}], "top_n": 10}}, "upstream": ["Generate:FortyBaboonsRule"]}, "Retrieval:BeigeBagsDress": {"downstream": ["Generate:BrightCitiesSink"], "obj": {"component_name": "Retrieval", "inputs": [], "output": null, "params": {"debug_inputs": [], "empty_response": "", "inputs": [], "kb_ids": [], "keywords_similarity_weight": 0.3, "message_history_window_size": 22, "output": null, "output_var_name": "output", "query": [{"component_id": "Generate:QuietMelonsHear", "type": "reference"}], "rerank_id": "", "similarity_threshold": 0.2, "top_k": 1024, "top_n": 8}}, "upstream": ["Generate:QuietMelonsHear"]}, "begin": {"downstream": ["Answer:FlatRavensPush"], "obj": {"component_name": "<PERSON><PERSON>", "inputs": [], "output": null, "params": {"debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "prologue": "Hi! I'm your smart assistant. What can I do for you?", "query": []}}, "upstream": []}}, "embed_id": "", "graph": {"edges": [{"id": "reactflow__edge-begin-Answer:FlatRavensPushc", "markerEnd": "logo", "source": "begin", "sourceHandle": null, "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:FlatRavensPush", "targetHandle": "c", "type": "buttonEdge"}, {"id": "reactflow__edge-Answer:FlatRavensPushb-Generate:QuietMelonsHearc", "markerEnd": "logo", "source": "Answer:FlatRavensPush", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:QuietMelonsHear", "targetHandle": "c", "type": "buttonEdge"}, {"id": "reactflow__edge-Answer:FlatRavensPushb-Generate:FortyBaboonsRulec", "markerEnd": "logo", "source": "Answer:FlatRavensPush", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:FortyBaboonsRule", "targetHandle": "c", "type": "buttonEdge"}, {"id": "reactflow__edge-Generate:FortyBaboonsRuleb-PubMed:TwentyFansShakec", "markerEnd": "logo", "source": "Generate:FortyBaboonsRule", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "PubMed:TwentyFansShake", "targetHandle": "c", "type": "buttonEdge"}, {"id": "reactflow__edge-Generate:QuietMelonsHearb-Retrieval:BeigeBagsDressc", "markerEnd": "logo", "source": "Generate:QuietMelonsHear", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Retrieval:BeigeBagsDress", "targetHandle": "c", "type": "buttonEdge"}, {"id": "xy-edge__Retrieval:BeigeBagsDressb-Generate:BrightCitiesSinkb", "markerEnd": "logo", "source": "Retrieval:BeigeBagsDress", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:BrightCitiesSink", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__PubMed:TwentyFansShakeb-Generate:BrightCitiesSinkb", "markerEnd": "logo", "source": "PubMed:TwentyFansShake", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:BrightCitiesSink", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:BrightCitiesSinkc-Answer:FlatRavensPushc", "markerEnd": "logo", "source": "Generate:BrightCitiesSink", "sourceHandle": "c", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:FlatRavensPush", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}], "nodes": [{"data": {"label": "<PERSON><PERSON>", "name": "opening"}, "dragging": false, "height": 44, "id": "begin", "measured": {"height": 44, "width": 100}, "position": {"x": -599.8361708291377, "y": 161.91688790133628}, "positionAbsolute": {"x": -599.8361708291377, "y": 161.91688790133628}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode"}, {"data": {"form": {"email": "<EMAIL>", "query": [{"component_id": "Generate:FortyBaboonsRule", "type": "reference"}], "top_n": 10}, "label": "PubMed", "name": "Search PubMed"}, "dragging": false, "height": 44, "id": "PubMed:TwentyFansShake", "measured": {"height": 44, "width": 200}, "position": {"x": 388.4151716305788, "y": 272.51398951401995}, "positionAbsolute": {"x": 389.7229173847695, "y": 276.4372267765921}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "ragNode", "width": 200}, {"data": {"form": {}, "label": "Answer", "name": "Interface"}, "dragging": false, "height": 44, "id": "Answer:FlatRavensPush", "measured": {"height": 44, "width": 200}, "position": {"x": -277.4280835723395, "y": 162.89713236919926}, "positionAbsolute": {"x": -370.881803561134, "y": 161.41373998842477}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "logicNode", "width": 200}, {"data": {"form": {"cite": true, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": true, "max_tokens": 256, "message_history_window_size": 12, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "Role: You are a professional medical consulting translation assistant\n\nTask: Translate user questions into Chinese, ensuring accuracy of medical terminology and appropriateness of context.\n\nRequirements:\n- Accurately translate medical terminology to convey the integrity and emotional color of the original message.\n- For unclear or uncertain medical terminology, the original text may be retained to ensure accuracy.\n- Respect the privacy and sensitivity of medical consultations and ensure that sensitive information is not disclosed during the translation process.\n- If the user's question is in Chinese, there is no need to translate, just output the user's question directly\n\nExample:\nOriginal (English): Doctor, I have been suffering from chest pain and shortness of breath for the past few days.\nTranslation (Chinese): 医生，我这几天一直胸痛和气短。\n\nNote:\nOnly the translated content needs to be output, no other irrelevant content!", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "Translate to Chinese"}, "dragging": false, "height": 86, "id": "Generate:QuietMelonsHear", "measured": {"height": 86, "width": 200}, "position": {"x": -2.756518132081453, "y": 38.86485966020132}, "positionAbsolute": {"x": -2.756518132081453, "y": 38.86485966020132}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode", "width": 200}, {"data": {"form": {"cite": false, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": true, "max_tokens": 256, "message_history_window_size": 1, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "Role: You are a professional Chinese-English medical question translation assistant\n\nTask: Accurately translate users' Chinese medical question content into English, ensuring accuracy of terminology and clarity of expression\n\nRequirements:\n- In-depth understanding of the terminology and disease descriptions in Chinese medical inquiries to ensure correct medical vocabulary is used in the English translation.\n- Maintain the semantic integrity and accuracy of the original text to avoid omitting important information or introducing errors.\n- Pay attention to the differences in expression habits between Chinese and English, and make appropriate adjustments to make the English translation more natural and fluent.\n- Respect the patient's privacy and the principle of medical confidentiality, and do not disclose any sensitive information during the translation process.\n\nExample：\nOriginal sentence: 我最近总是感觉胸闷，有时还会有心悸的感觉。\nTranslated: I've been feeling chest tightness recently, and sometimes I experience palpitations.\n\nNote:\nOnly the translated content should be given, do not output other irrelevant content!", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "Translate to English"}, "dragging": false, "height": 86, "id": "Generate:FortyBaboonsRule", "measured": {"height": 86, "width": 200}, "position": {"x": -3.825864707727135, "y": 253.2285157283701}, "positionAbsolute": {"x": -3.825864707727135, "y": 253.2285157283701}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode", "width": 200}, {"data": {"form": {"kb_ids": [], "keywords_similarity_weight": 0.3, "query": [{"component_id": "Generate:QuietMelonsHear", "type": "reference"}], "similarity_threshold": 0.2, "top_n": 8}, "label": "Retrieval", "name": "Search Q&A"}, "dragging": false, "height": 44, "id": "Retrieval:BeigeBagsDress", "measured": {"height": 44, "width": 200}, "position": {"x": 316.9462115194757, "y": 57.81358887451738}, "positionAbsolute": {"x": 382.25527986090765, "y": 35.38705653631584}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "retrievalNode", "width": 200}, {"data": {"form": {"text": "Receives the user's financial inquiries and displays the large model's response to financial questions."}, "label": "Note", "name": "N: Interface"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 162, "id": "Note:RedZebrasEnjoy", "measured": {"height": 162, "width": 200}, "position": {"x": -274.75115571622416, "y": 233.92632661399952}, "positionAbsolute": {"x": -374.13983303471906, "y": 219.54112331790157}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 162, "width": 200}, "targetPosition": "left", "type": "noteNode", "width": 200}, {"data": {"form": {"text": "Translate user's question to English by LLM."}, "label": "Note", "name": "N: Translate to English"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 128, "id": "Note:DarkIconsClap", "measured": {"height": 128, "width": 227}, "position": {"x": -2.0308204014422273, "y": 379.60045703973515}, "positionAbsolute": {"x": -0.453362859534991, "y": 357.3687792184929}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 128, "width": 204}, "targetPosition": "left", "type": "noteNode", "width": 227}, {"data": {"form": {"text": "Translate user's question to Chinese by LLM."}, "label": "Note", "name": "N: Translate to Chinese"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 128, "id": "Note:SmallRiversTap", "measured": {"height": 128, "width": 220}, "position": {"x": -2.9326060127226583, "y": -99.3117253460485}, "positionAbsolute": {"x": -5.453362859535048, "y": -105.63122078150693}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 128, "width": 196}, "targetPosition": "left", "type": "noteNode", "width": 220}, {"data": {"form": {"text": "PubMed® comprises more than 37 million citations for biomedical literature from MEDLINE, life science journals, and online books. Citations may include links to full text content from PubMed Central and publisher web sites."}, "label": "Note", "name": "N: Search PubMed"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 220, "id": "Note:MightyDeerShout", "measured": {"height": 220, "width": 287}, "position": {"x": 718.5466371404648, "y": 275.36877921849293}, "positionAbsolute": {"x": 718.5466371404648, "y": 275.36877921849293}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 220, "width": 287}, "targetPosition": "left", "type": "noteNode", "width": 287}, {"data": {"form": {"text": "You can download the Q&A dataset at\nhttps://huggingface.co/datasets/InfiniFlow/medical_QA"}, "label": "Note", "name": "N: Search Q&A"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 128, "id": "Note:VioletSuitsFlash", "measured": {"height": 128, "width": 387}, "position": {"x": 776.4332169584197, "y": 32.89802610798361}, "positionAbsolute": {"x": 776.4332169584197, "y": 32.89802610798361}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 128, "width": 387}, "targetPosition": "left", "type": "noteNode", "width": 387}, {"data": {"form": {"text": "A prompt summarize content from search result from PubMed and Q&A dataset."}, "label": "Note", "name": "N: LLM"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 140, "id": "Note:BeigeCoinsBuild", "measured": {"height": 140, "width": 281}, "position": {"x": 293.89948660403513, "y": -238.31673896113236}, "positionAbsolute": {"x": 756.9053449234701, "y": -212.92342186138177}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 281}, {"data": {"form": {"cite": true, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 12, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "Role: You are a professional medical consulting assistant\n\nTasks: Answer questions posed by users. Answer based on content provided by the knowledge base, PubMed\n\nRequirement:\n- Answers may refer to the content provided (Knowledge Base, PubMed).\n- If the provided PubMed content is referenced, a link to the corresponding URL should be given.\n-Answers should be professional and accurate; no information should be fabricated that is not relevant to the user's question.\n\nProvided knowledge base content\n{Retrieval:BeigeBagsDress}\n\nPubMed content provided\n\n{PubMed:TwentyFansShake}", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "LLM"}, "dragging": false, "id": "Generate:BrightCitiesSink", "measured": {"height": 106, "width": 200}, "position": {"x": 300, "y": -86.3689104694316}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}]}, "history": [], "messages": [], "path": [], "reference": []}, "avatar": "data:image/png;base64,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"}