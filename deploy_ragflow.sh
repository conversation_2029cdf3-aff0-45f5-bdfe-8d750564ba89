#!/bin/bash

# RAGFlow部署脚本 - 复用现有MySQL和Redis
set -e

echo "🚀 开始部署RAGFlow..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查Docker和Docker Compose
echo "📋 检查环境依赖..."
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo -e "${RED}❌ Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi

# 检查系统资源
echo "💾 检查系统资源..."
TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.0f", $2}')
if [ $TOTAL_MEM -lt 8192 ]; then
    echo -e "${YELLOW}⚠️  警告: 系统内存少于8GB，可能影响性能${NC}"
fi

# 检查端口占用
echo "🔍 检查端口占用..."
PORTS=(8080 9000 9001 9200)
for port in "${PORTS[@]}"; do
    if netstat -tuln | grep ":$port " > /dev/null; then
        echo -e "${RED}❌ 端口 $port 已被占用，请修改配置或停止占用该端口的服务${NC}"
        exit 1
    fi
done

# 设置vm.max_map_count（Elasticsearch需要）
echo "⚙️  配置系统参数..."
current_max_map_count=$(sysctl vm.max_map_count | cut -d' ' -f3)
if [ $current_max_map_count -lt 262144 ]; then
    echo "设置vm.max_map_count=262144"
    sudo sysctl -w vm.max_map_count=262144
    echo "vm.max_map_count=262144" | sudo tee -a /etc/sysctl.conf
fi

# 创建必要的目录
echo "📁 创建目录结构..."
mkdir -p ragflow_data/{logs,minio,elasticsearch}

# 配置文件检查
echo "📝 检查配置文件..."
if [ ! -f "service_conf_custom.yaml" ]; then
    echo -e "${RED}❌ 配置文件 service_conf_custom.yaml 不存在${NC}"
    exit 1
fi

# 提示用户修改配置
echo -e "${YELLOW}⚠️  请确保已修改 service_conf_custom.yaml 中的以下配置：${NC}"
echo "   - MySQL密码"
echo "   - Redis密码"
echo "   - OpenAI API密钥"
echo ""
read -p "是否已完成配置修改？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}请先修改配置文件后再运行此脚本${NC}"
    exit 1
fi

# 在现有MySQL中创建RAGFlow数据库
echo "🗄️  准备数据库..."
echo -e "${YELLOW}请手动在MySQL中创建RAGFlow数据库：${NC}"
echo "mysql -h ************** -P 13306 -u root -p"
echo "CREATE DATABASE IF NOT EXISTS ragflow_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
echo ""
read -p "是否已创建数据库？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}请先创建数据库后再继续${NC}"
    exit 1
fi

# 启动服务
echo "🚀 启动RAGFlow服务..."
docker-compose -f docker-compose-custom.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f docker-compose-custom.yml ps

# 检查RAGFlow服务健康状态
echo "🏥 检查RAGFlow健康状态..."
for i in {1..30}; do
    if curl -s http://localhost:8080 > /dev/null; then
        echo -e "${GREEN}✅ RAGFlow服务启动成功！${NC}"
        break
    fi
    echo "等待RAGFlow启动... ($i/30)"
    sleep 10
done

echo ""
echo -e "${GREEN}🎉 RAGFlow部署完成！${NC}"
echo ""
echo "📋 服务信息："
echo "   - RAGFlow Web界面: http://**************:8080"
echo "   - MinIO控制台: http://**************:9001"
echo "   - Elasticsearch: http://**************:9200"
echo ""
echo "📝 默认登录信息："
echo "   - MinIO: ragflow_admin / ragflow_minio_2024"
echo "   - Elasticsearch: elastic / infini_rag_flow"
echo ""
echo "🔧 管理命令："
echo "   - 查看日志: docker-compose -f docker-compose-custom.yml logs -f"
echo "   - 停止服务: docker-compose -f docker-compose-custom.yml down"
echo "   - 重启服务: docker-compose -f docker-compose-custom.yml restart"
echo ""
echo -e "${YELLOW}⚠️  首次启动可能需要几分钟时间下载模型和初始化数据库${NC}"
