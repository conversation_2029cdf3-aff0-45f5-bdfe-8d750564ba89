# RAGFlow服务配置 - 连接现有MySQL和Redis
ragflow:
  host: 0.0.0.0
  http_port: 9380

# 使用现有MySQL服务
mysql:
  name: 'ragflow_db'
  user: 'root'
  password: 'jlwl0617!'
  host: '**************'
  port: 13306
  max_connections: 900
  stale_timeout: 300

# 使用现有Redis服务
redis:
  db: 2
  password: ''  # Redis没有设置密码
  host: '**************:16379'

# Elasticsearch配置
es:
  hosts: 'http://es01:9200'
  username: 'elastic'
  password: 'infini_rag_flow'

# MinIO配置
minio:
  user: 'ragflow_admin'
  password: 'ragflow_minio_2024'
  host: 'minio:9000'

# 用户默认LLM配置
user_default_llm:
  "factory": "OpenAI"
  "api_key": "sk-placeholder-key"
  "base_url": "https://api.openai.com/v1"

# 其他配置
chunk_token_num: 128
retrieve_method: "hybrid"
retrieve_top_k: 6

llm:
  OpenAI:
    base_url: "https://api.openai.com/v1"
    api_key: "sk-placeholder-key"
