#!/bin/bash

#
# RAGFlow 后台启动脚本
# 用法: ./start_ragflow_daemon.sh [start|stop|restart|status]
#

RAGFLOW_DIR="/data/ragflow"
PID_FILE="$RAGFLOW_DIR/ragflow.pid"
LOG_FILE="$RAGFLOW_DIR/logs/ragflow_daemon.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
    echo "[ERROR] $1" >> "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    echo "[SUCCESS] $1" >> "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    echo "[WARNING] $1" >> "$LOG_FILE"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    local deps=("python3" "docker")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            error "依赖 $dep 未安装"
            exit 1
        fi
    done
}

# 创建必要目录
create_directories() {
    mkdir -p "$(dirname "$LOG_FILE")"
    mkdir -p "$(dirname "$PID_FILE")"
}

# 检查进程是否运行
is_running() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 获取进程PID
get_pid() {
    if [[ -f "$PID_FILE" ]]; then
        cat "$PID_FILE"
    else
        echo ""
    fi
}

# 设置Tika服务
setup_tika_service() {
    log "检查Tika服务状态..."

    # 检查Docker Tika服务是否运行
    if docker ps | grep -q "ragflow-tika"; then
        log "Docker Tika服务已运行"
    else
        log "启动Docker Tika服务..."

        # 停止可能存在的同名容器
        docker stop ragflow-tika 2>/dev/null || true
        docker rm ragflow-tika 2>/dev/null || true

        # 启动新的Tika容器
        if docker run -d --name ragflow-tika -p 9998:9998 apache/tika:latest; then
            log "Docker Tika服务启动成功"

            # 等待Tika服务就绪
            local count=0
            while [[ $count -lt 15 ]]; do
                if curl -s http://localhost:9998/version > /dev/null 2>&1; then
                    success "Tika服务就绪"
                    break
                fi
                sleep 2
                ((count++))
                echo -n "."
            done

            if [[ $count -ge 15 ]]; then
                warning "Tika服务启动超时，但容器已启动"
            fi
        else
            error "Docker Tika服务启动失败"
        fi
    fi

    # 测试Tika连接
    if curl -s http://localhost:9998/version > /dev/null 2>&1; then
        success "Tika服务连接正常"
    else
        warning "Tika服务连接失败，Word/Excel解析可能受影响"
    fi
}

# 停止Tika服务
stop_tika_service() {
    log "停止Tika服务..."

    if docker ps | grep -q "ragflow-tika"; then
        docker stop ragflow-tika 2>/dev/null || true
        docker rm ragflow-tika 2>/dev/null || true
        success "Tika服务已停止"
    else
        log "Tika服务未运行"
    fi
}

# 启动RAGFlow
start_ragflow() {
    log "开始启动RAGFlow服务..."
    
    # 检查是否已经运行
    if is_running; then
        warning "RAGFlow服务已经在运行中 (PID: $(get_pid))"
        return 0
    fi
    
    # 检查端口占用
    if netstat -tlnp | grep -q ":9380 "; then
        error "端口9380已被占用，请检查是否有其他RAGFlow实例运行"
        return 1
    fi
    
    # 切换到RAGFlow目录
    cd "$RAGFLOW_DIR" || {
        error "无法切换到RAGFlow目录: $RAGFLOW_DIR"
        return 1
    }
    
    # 设置环境变量
    export PYTHONPATH="$RAGFLOW_DIR:"
    export STORAGE_IMPL="AWS_S3"
    export DOC_ENGINE="elasticsearch"
    export NLTK_DATA="$RAGFLOW_DIR/nltk_data"
    export LIGHTEN=1  # 跳过复杂的NLP处理，避免NLTK依赖问题

    # Tika服务器配置
    export TIKA_SERVER_ENDPOINT="http://localhost:9998"
    export TIKA_CLIENT_ONLY=true

    # 启动Docker Tika服务器（如果未运行）
    setup_tika_service
    

    
    # 后台启动RAGFlow服务器
    log "启动RAGFlow服务器..."
    nohup python3 api/ragflow_server.py > "$LOG_FILE" 2>&1 &
    local server_pid=$!

    # 后台启动任务执行器
    log "启动任务执行器..."
    nohup python3 rag/svr/task_executor.py > logs/task_executor.log 2>&1 &
    local executor_pid=$!

    # 保存主服务PID
    echo "$server_pid" > "$PID_FILE"

    # 保存任务执行器PID
    echo "$executor_pid" > "${PID_FILE}.executor"
    
    # 等待服务启动
    log "等待服务启动..."
    local count=0
    while [[ $count -lt 30 ]]; do
        if curl -s http://localhost:9380/health > /dev/null 2>&1; then
            success "RAGFlow服务启动成功 (PID: $pid)"
            success "服务地址: http://localhost:9380"
            success "API文档: http://localhost:9380/apidocs"
            return 0
        fi
        sleep 2
        ((count++))
        echo -n "."
    done
    
    error "RAGFlow服务启动失败，请检查日志: $LOG_FILE"
    stop_ragflow
    return 1
}

# 停止RAGFlow
stop_ragflow() {
    log "停止RAGFlow服务..."

    if ! is_running; then
        warning "RAGFlow服务未运行"
        return 0
    fi

    local pid=$(get_pid)
    log "正在停止进程 (PID: $pid)..."

    # 停止任务执行器
    if [[ -f "${PID_FILE}.executor" ]]; then
        local executor_pid=$(cat "${PID_FILE}.executor")
        if ps -p "$executor_pid" > /dev/null 2>&1; then
            log "正在停止任务执行器 (PID: $executor_pid)..."
            kill -TERM "$executor_pid" 2>/dev/null
            sleep 2
            if ps -p "$executor_pid" > /dev/null 2>&1; then
                kill -KILL "$executor_pid" 2>/dev/null
            fi
        fi
        rm -f "${PID_FILE}.executor"
    fi

    # 优雅停止主服务
    kill -TERM "$pid" 2>/dev/null

    # 等待进程结束
    local count=0
    while [[ $count -lt 10 ]]; do
        if ! ps -p "$pid" > /dev/null 2>&1; then
            success "RAGFlow服务已停止"
            rm -f "$PID_FILE"
            return 0
        fi
        sleep 1
        ((count++))
    done

    # 强制停止
    warning "优雅停止失败，强制终止进程..."
    kill -KILL "$pid" 2>/dev/null
    rm -f "$PID_FILE"
    success "RAGFlow服务已强制停止"

    # 停止Tika服务
    stop_tika_service
}

# 重启RAGFlow
restart_ragflow() {
    log "重启RAGFlow服务..."
    stop_ragflow
    sleep 2
    start_ragflow
}

# 查看状态
status_ragflow() {
    echo "=== RAGFlow 服务状态 ==="
    
    if is_running; then
        local pid=$(get_pid)
        success "RAGFlow服务正在运行 (PID: $pid)"
        
        # 检查端口
        if netstat -tlnp | grep -q ":9380 "; then
            success "端口9380正常监听"
        else
            warning "端口9380未监听"
        fi
        
        # 检查健康状态
        if curl -s http://localhost:9380/health > /dev/null 2>&1; then
            success "服务健康检查通过"
        else
            warning "服务健康检查失败"
        fi
        
        echo "服务地址: http://localhost:9380"
        echo "API文档: http://localhost:9380/apidocs"
        echo "日志文件: $LOG_FILE"
        echo "PID文件: $PID_FILE"
        
    else
        warning "RAGFlow服务未运行"
    fi
    
    echo ""
    echo "=== 系统资源使用情况 ==="
    echo "内存使用:"
    free -h
    echo ""
    echo "磁盘使用:"
    df -h "$RAGFLOW_DIR"
}

# 主函数
main() {
    check_root
    create_directories
    
    case "${1:-start}" in
        start)
            start_ragflow
            ;;
        stop)
            stop_ragflow
            ;;
        restart)
            restart_ragflow
            ;;
        status)
            status_ragflow
            ;;
        *)
            echo "用法: $0 {start|stop|restart|status}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动RAGFlow服务"
            echo "  stop    - 停止RAGFlow服务"
            echo "  restart - 重启RAGFlow服务"
            echo "  status  - 查看服务状态"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
