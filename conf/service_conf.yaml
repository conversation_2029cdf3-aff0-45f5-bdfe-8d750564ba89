ragflow:
  host: 0.0.0.0
  http_port: 9380
  secret_key: 'ragflow_secret_key_for_sms_auth_system_2025'
mysql:
  name: 'ragflow_db'
  user: 'root'
  password: 'jlwl0617!'
  host: '**************'
  port: 13306
  max_connections: 900
  stale_timeout: 300
  max_allowed_packet: 1073741824
s3:
  access_key: 'AKIDwlMkvtd9SabP0NXUEn8F9pIXYQ9ZmmYH'
  secret_key: 'c1DC61pYEoBLI2YrwqY3KNDpFSYR6Ymn'
  region: 'ap-chengdu'
  endpoint_url: 'https://cos.ap-chengdu.myqcloud.com'
  bucket: 'jinglveweilai-**********'
  signature_version: 'v4'
  addressing_style: 'virtual'
es:
  hosts: 'http://**************:19200'
  username: 'elastic'
  password: 'infini_rag_flow'
os:
  hosts: 'http://**************:1201'
  username: 'admin'
  password: 'infini_rag_flow_OS_01'
infinity:
  uri: 'localhost:23817'
  db_name: 'default_db'
redis:
  db: 2
  password: 'jlwl0617'
  host: '**************:16379'
sms:
  # 阿里云短信服务配置
  provider: 'aliyun'
  access_key_id: 'LTAI5tMEEgATo98sTP6Vwrk7'  # 请填入阿里云AccessKey ID
  access_key_secret: '******************************'  # 请填入阿里云AccessKey Secret
  region: 'cn-hangzhou'  # 阿里云短信服务区域
  sign_name: '重庆经略未来企业服务集团'  # 短信签名，请填入已审核通过的签名
  template_code: 'SMS_490125044'  # 短信模板CODE，请填入已审核通过的模板
  # 验证码配置
  code_length: 6  # 验证码长度
  code_expire: 300  # 验证码过期时间（秒）
  send_interval: 60  # 发送间隔（秒）
minio:  # 保留配置以避免启动错误，但不使用
  user: 'ragflow_admin'
  password: 'infini_rag_flow'
  host: '**************:19000'
# postgres:
#   name: 'rag_flow'
#   user: 'rag_flow'
#   password: 'infini_rag_flow'
#   host: 'postgres'
#   port: 5432
#   max_connections: 100
#   stale_timeout: 30
# s3:
#   access_key: 'access_key'
#   secret_key: 'secret_key'
#   region: 'region'
# oss:
#   access_key: 'access_key'
#   secret_key: 'secret_key'
#   endpoint_url: 'http://oss-cn-hangzhou.aliyuncs.com'
#   region: 'cn-hangzhou'
#   bucket: 'bucket_name'
# azure:
#   auth_type: 'sas'
#   container_url: 'container_url'
#   sas_token: 'sas_token'
# azure:
#   auth_type: 'spn'
#   account_url: 'account_url'
#   client_id: 'client_id'
#   secret: 'secret'
#   tenant_id: 'tenant_id'
#   container_name: 'container_name'
# The OSS object storage uses the MySQL configuration above by default. If you need to switch to another object storage service, please uncomment and configure the following parameters.
# opendal:
#   scheme: 'mysql'  # Storage type, such as s3, oss, azure, etc.
#   config:
#     oss_table: 'your_table_name'
user_default_llm:
  factory: 'Tongyi-Qianwen'
  api_key: 'sk-f93fb0e4227c4c3d8d3badae86d87833'
  base_url: ''
  default_models:
    chat_model: 'qwen-plus'
    embedding_model: 'text-embedding-v3'
    rerank_model: ''
    asr_model: ''
    image2text_model: ''
  deep_thinking:
    enabled: true
    models: ['qwq-plus', 'deepseek-r1', 'qwen-plus']
    default_model: 'qwq-plus'
    enable_thinking_param: true  # 对于qwen-plus需要设置enable_thinking参数
# oauth:
#   oauth2:
#     display_name: "OAuth2"
#     client_id: "your_client_id"
#     client_secret: "your_client_secret"
#     authorization_url: "https://your-oauth-provider.com/oauth/authorize"
#     token_url: "https://your-oauth-provider.com/oauth/token"
#     userinfo_url: "https://your-oauth-provider.com/oauth/userinfo"
#     redirect_uri: "https://your-app.com/v1/user/oauth/callback/oauth2"
#   oidc:
#     display_name: "OIDC"
#     client_id: "your_client_id"
#     client_secret: "your_client_secret"
#     issuer: "https://your-oauth-provider.com/oidc"
#     scope: "openid email profile"
#     redirect_uri: "https://your-app.com/v1/user/oauth/callback/oidc"
#   github:
#     type: "github"
#     icon: "github"
#     display_name: "Github"
#     client_id: "your_client_id"
#     client_secret: "your_client_secret"
#     redirect_uri: "https://your-app.com/v1/user/oauth/callback/github"
# authentication:
#   client:
#     switch: false
#     http_app_key:
#     http_secret_key:
#   site:
#     switch: false
# permission:
#   switch: false
#   component: false
#   dataset: false

# Tika服务器配置
tika:
  server_endpoint: "http://localhost:9998"
  client_only: true

# 通义千问OCR配置
qwen_ocr:
  enabled: true
  model: "qwen-vl-ocr-latest"
  api_key: "sk-f93fb0e4227c4c3d8d3badae86d87833"
  base_url: "https://dashscope.aliyuncs.com/compatible-mode/v1"
