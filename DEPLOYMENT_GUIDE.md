# RAGFlow 部署和使用指南

## 服务配置

### 1. 后台服务管理

使用 `start_ragflow_daemon.sh` 脚本管理RAGFlow服务：

```bash
# 启动服务
./start_ragflow_daemon.sh start

# 停止服务
./start_ragflow_daemon.sh stop

# 重启服务
./start_ragflow_daemon.sh restart

# 查看状态
./start_ragflow_daemon.sh status
```

### 2. Nginx 路由配置

RAGFlow API已集成到现有的nginx配置中：

**配置文件**: `/data/nginx/conf/conf.d/jinglueai.cn.conf`

**路由配置**:
- **API接口**: `https://jinglueai.cn/rag-api/` → `http://172.30.0.9:9380/v1/`
- **API文档**: `https://jinglueai.cn/rag-apidocs` → `http://172.30.0.9:9380/apidocs`

**重载nginx配置**:
```bash
./reload_nginx.sh
```

## API 使用

### 1. 接口地址

- **基础URL**: `https://jinglueai.cn/rag-api/`
- **API文档**: `https://jinglueai.cn/rag-apidocs`

### 2. 认证方式

- **短信登录接口**: 无需认证，直接调用
- **人员管理接口**: 需要管理员权限，请求头携带 `Authorization: {access_token}`

### 3. 调用示例

#### 登录获取token
```bash
# 1. 发送验证码
curl -X POST https://jinglueai.cn/rag-api/sms-auth/send-code \
  -H "Content-Type: application/json" \
  -d '{"phone":"18502312097"}'

# 2. 验证码登录
curl -X POST https://jinglueai.cn/rag-api/sms-auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone":"18502312097","code":"123456","remember_me":true}'
```

#### 人员管理
```bash
# 获取人员列表
curl -X GET https://jinglueai.cn/rag-api/staff/list \
  -H "Authorization: {access_token}"

# 添加人员
curl -X POST https://jinglueai.cn/rag-api/staff/add \
  -H "Content-Type: application/json" \
  -H "Authorization: {access_token}" \
  -d '{"phone":"13999999999","nickname":"新用户"}'

# 删除人员
curl -X DELETE https://jinglueai.cn/rag-api/staff/delete/{user_id} \
  -H "Authorization: {access_token}"
```

## 技术特性

### 1. SSE 支持
nginx配置已支持Server-Sent Events (SSE)：
- 关闭缓冲 (`proxy_buffering off`)
- 启用分块传输 (`chunked_transfer_encoding on`)
- 保持连接开启 (`proxy_set_header Connection ''`)

### 2. CORS 支持
自动处理跨域请求：
- 支持所有来源 (`Access-Control-Allow-Origin *`)
- 支持常用HTTP方法
- 自动处理OPTIONS预检请求

### 3. 大文件上传
支持最大100MB文件上传 (`client_max_body_size 100M`)

### 4. 超时设置
- 连接超时: 300秒
- 读取超时: 300秒
- 发送超时: 300秒

## 监控和日志

### 1. 服务日志
- **RAGFlow日志**: `/data/ragflow/logs/ragflow_daemon.log`
- **服务器日志**: `/data/ragflow/logs/ragflow_server.log`

### 2. Nginx日志
- **访问日志**: `/var/log/nginx/access.log`
- **错误日志**: `/var/log/nginx/error.log`

### 3. 健康检查
```bash
# 检查RAGFlow服务状态
./start_ragflow_daemon.sh status

# 检查nginx状态
./reload_nginx.sh

# 直接访问健康检查接口
curl http://localhost:9380/health
```

## 故障排除

### 1. 服务启动失败
```bash
# 检查端口占用
netstat -tlnp | grep 9380

# 查看详细日志
tail -f /data/ragflow/logs/ragflow_daemon.log

# 手动启动调试
cd /data/ragflow
python3 api/ragflow_server.py
```

### 2. nginx配置问题
```bash
# 测试配置语法
docker exec jlwl-nginx nginx -t

# 查看nginx错误日志
docker logs jlwl-nginx

# 重启nginx容器
docker restart jlwl-nginx
```

### 3. API访问问题
```bash
# 测试本地接口
curl http://localhost:9380/v1/staff/list

# 测试nginx代理
curl https://jinglueai.cn/rag-api/staff/list

# 检查防火墙
iptables -L
```

## 安全注意事项

1. **管理员权限**: 只有is_superuser=true的用户才能管理人员
2. **Token验证**: 人员管理接口需要有效的access_token
3. **HTTPS**: 生产环境强制使用HTTPS
4. **日志监控**: 定期检查访问日志和错误日志

## 维护建议

1. **定期备份**: 备份数据库和配置文件
2. **日志轮转**: 配置日志文件自动轮转
3. **监控告警**: 设置服务状态监控
4. **性能优化**: 根据访问量调整nginx和RAGFlow配置

---

**部署完成！**

- 🚀 **服务地址**: https://jinglueai.cn/rag-api/
- 📚 **API文档**: https://jinglueai.cn/rag-apidocs
- 🔧 **管理脚本**: `./start_ragflow_daemon.sh`
- ⚙️ **nginx重载**: `./reload_nginx.sh`
