# Generated by Cargo
# will have compiled files and executables
debug/
target/
__pycache__/
hudet/
cv/
layout_app.py
api/flask_session

# Remove Cargo.lock from gitignore if creating an executable, leave it for libraries
# More information here https://doc.rust-lang.org/cargo/guide/cargo-toml-vs-cargo-lock.html
Cargo.lock

# These are backup files generated by rustfmt
**/*.rs.bk

# MSVC Windows builds of rustc generate these, which store debugging information
*.pdb
*.trie

.idea/
.vscode/

# Exclude Mac generated files
.DS_Store

# Exclude the log folder
docker/ragflow-logs/
/flask_session
/logs
rag/res/deepdoc

# Exclude sdk generated files
sdk/python/ragflow.egg-info/
sdk/python/build/
sdk/python/dist/
sdk/python/ragflow_sdk.egg-info/

# Exclude dep files
libssl*.deb
tika-server*.jar*
cl100k_base.tiktoken
chrome*
huggingface.co/
nltk_data/

# Exclude hash-like temporary files like 9b5ad71b2ce5302211f9c61530b329a4922fc6a4
*[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f]*
.lh/
.venv
docker/data


#--------------------------------------------------#
# The following was generated with gitignore.nvim: #
#--------------------------------------------------#
# Gitignore for the following technologies: Node

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
# dist  # 注释掉dist，允许推送前端构建文件

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Serverless Webpack directories
.webpack/

# SvelteKit build / generate output
.svelte-kit

# 排除前端构建和大文件，主要推送后端代码
web/
web/dist/
web/node_modules/
web/.umi/
web/.umi-production/
web/src/assets/svg/
rag/res/deepdoc/
rag/res/huggingface.co/
rag/res/*.trie
logs/
*.log
*.trie
*.deb
*.jar
*.tiktoken
*.onnx
*.model
*.zip
chrome*
chromedriver*
huggingface.co/
nltk_data/
tika-server*
9b5ad71b2ce5302211f9c61530b329a4922fc6a4
flask_session/

# 排除所有大于1MB的二进制文件
*.so
*.dylib
*.dll
*.exe

# 排除模型和数据文件
*.bin
*.pt
*.pth
*.safetensors
*.h5
*.pkl
*.pickle

# 排除临时和缓存文件
.cache/
.tmp/
tmp/
temp/
__pycache__/
*.pyc
*.pyo
.pytest_cache/

test/