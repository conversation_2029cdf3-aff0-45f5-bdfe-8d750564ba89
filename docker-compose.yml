# RAGFlow部署配置 - 复用现有MySQL和Redis
version: '3.8'

services:
  # Elasticsearch - 向量存储和全文搜索
  es01:
    container_name: ragflow-es-01
    image: elasticsearch:8.11.3
    volumes:
      - ragflow_esdata:/usr/share/elasticsearch/data
    ports:
      - "19200:9200"
    environment:
      - node.name=es01
      - ELASTIC_PASSWORD=infini_rag_flow
      - bootstrap.memory_lock=false
      - discovery.type=single-node
      - xpack.security.enabled=true
      - xpack.security.http.ssl.enabled=false
      - xpack.security.transport.ssl.enabled=false
      - cluster.routing.allocation.disk.watermark.low=5gb
      - cluster.routing.allocation.disk.watermark.high=3gb
      - cluster.routing.allocation.disk.watermark.flood_stage=2gb
      - TZ=Asia/Shanghai
    mem_limit: 4g
    ulimits:
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test: ["CMD-SHELL", "curl http://localhost:9200"]
      interval: 10s
      timeout: 10s
      retries: 120
    networks:
      - ragflow
    restart: unless-stopped

  # MinIO - 对象存储
  minio:
    image: quay.io/minio/minio:RELEASE.2023-12-20T01-00-02Z
    container_name: ragflow-minio
    command: server --console-address ":9001" /data
    ports:
      - "19000:9000"
      - "19001:9001"
    environment:
      - MINIO_ROOT_USER=ragflow_admin
      - MINIO_ROOT_PASSWORD=ragflow_minio_2024
      - TZ=Asia/Shanghai
    volumes:
      - ragflow_minio_data:/data
    networks:
      - ragflow
    restart: unless-stopped

  # RAGFlow主服务
  ragflow:
    image: infiniflow/ragflow:v0.19.1-slim
    container_name: ragflow-server
    ports:
      - "18080:9380"
    volumes:
      - ./service_conf.yaml:/ragflow/conf/service_conf.yaml
      - ragflow_logs:/ragflow/logs
    environment:
      - TZ=Asia/Shanghai
      - HF_ENDPOINT=https://hf-mirror.com
    depends_on:
      - es01
      - minio
    networks:
      - ragflow
    restart: unless-stopped

volumes:
  ragflow_esdata:
    driver: local
  ragflow_minio_data:
    driver: local
  ragflow_logs:
    driver: local

networks:
  ragflow:
    driver: bridge
