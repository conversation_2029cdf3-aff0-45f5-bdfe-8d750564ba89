# RAGFlow Sales Assistant Backend

## 🎯 项目概述

这是RAGFlow销售助手的后端代码仓库，基于RAGFlow开源项目构建，专门用于销售场景的智能问答系统。

## ✅ 当前状态

### 服务运行状态
- **后端API**: ✅ 运行在 http://localhost:9380
- **前端Web**: ✅ 运行在 http://localhost:3000  
- **数据库**: MySQL + Redis + Elasticsearch
- **存储**: 腾讯云COS (节省内存)

### 代码仓库
- **本地**: `/data/ragflow-lite` (轻量版，不含前端和大文件)
- **远程**: http://jinglueai.cn/gitlab/tongruiai/sales-assistant-backend.git
- **大小**: 18M (已移除web前端、模型文件等大文件)

## 🚀 快速启动

### 1. 启动后端服务
```bash
cd /data/ragflow
./start_ragflow.sh
```

### 2. 启动前端服务
```bash
cd /data/ragflow/web
python3 spa_server.py 3000 dist http://127.0.0.1:9380
```

### 3. 验证服务
- 后端API: http://localhost:9380
- 前端界面: http://localhost:3000

## 📁 项目结构

```
/data/ragflow-lite/          # 轻量版后端代码
├── api/                     # API接口层
├── rag/                     # RAG核心功能
├── agent/                   # 智能代理
├── deepdoc/                 # 文档解析
├── graphrag/                # 图RAG
├── conf/                    # 配置文件
├── start_ragflow.sh         # 启动脚本
└── PROJECT_STATUS.md        # 项目状态

/data/ragflow/               # 完整版本(含前端)
├── web/                     # 前端代码
├── logs/                    # 运行日志
└── ...                      # 其他文件
```

## 🔧 配置说明

### 主要配置文件
- `conf/service_conf.yaml` - 服务配置
- `conf/llm_factories.json` - LLM模型配置
- `conf/mapping.json` - 映射配置

### 存储配置
- **对象存储**: 腾讯云COS
- **文档引擎**: Elasticsearch
- **缓存**: Redis
- **数据库**: MySQL

## 🛠️ 开发说明

### 核心模块
1. **API层** (`api/`): Flask应用，提供RESTful API
2. **RAG引擎** (`rag/`): 检索增强生成核心
3. **文档解析** (`deepdoc/`): 支持多种文档格式
4. **智能代理** (`agent/`): 对话流程管理
5. **图RAG** (`graphrag/`): 知识图谱增强

### 启动流程
1. 加载配置文件
2. 初始化数据库连接
3. 启动Elasticsearch
4. 加载LLM模型
5. 启动Flask API服务

## 📋 API文档

主要API端点：
- `/v1/api/chat` - 对话接口
- `/v1/api/dataset` - 知识库管理
- `/v1/api/document` - 文档管理
- `/v1/api/chunk` - 文档片段管理

详细API文档请访问: http://localhost:9380/docs

## 🔍 故障排除

### 常见问题
1. **端口占用**: 检查9380和3000端口
2. **服务无响应**: 查看logs目录下的日志文件
3. **内存不足**: 确保系统有足够内存运行Elasticsearch

### 日志查看
```bash
tail -f /data/ragflow/logs/ragflow_server.log
```

## 📝 部署说明

### 生产环境部署
1. 配置域名和SSL证书
2. 设置反向代理(Nginx)
3. 配置数据库连接池
4. 设置监控和日志收集

### Docker部署
项目支持Docker部署，详见 `docker-compose.yml`

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 📄 许可证

本项目基于Apache 2.0许可证开源。

---

**最后更新**: 2025-07-03
**维护者**: 销售助手团队
