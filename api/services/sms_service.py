#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import json
import logging
import random
import time
from typing import Optional, Dict, Any

try:
    from alibabacloud_dysmsapi20170525.client import Client as DysmsapiClient
    from alibabacloud_tea_openapi import models as open_api_models
    from alibabacloud_dysmsapi20170525 import models as dysmsapi_models
    from alibabacloud_tea_util import models as util_models
    ALIYUN_SMS_AVAILABLE = True
except ImportError:
    ALIYUN_SMS_AVAILABLE = False
    logging.warning("阿里云短信SDK未安装，短信功能将不可用")

from api import settings
from api.db.services.common_service import CommonService


class SMSService:
    """短信验证码服务"""
    
    def __init__(self):
        self.redis_client = None
        self.sms_client = None
        self._initialized = False
        self._last_codes = {}  # 内存中存储最近的验证码（Redis不可用时使用）

    def _ensure_initialized(self):
        """确保服务已初始化"""
        if not self._initialized:
            self._init_redis()
            self._init_sms_client()
            self._initialized = True
    
    def _init_redis(self):
        """初始化Redis连接"""
        try:
            import redis
            redis_config = settings.REDIS
            if not redis_config:
                logging.warning("Redis配置不存在，跳过Redis连接")
                return

            self.redis_client = redis.Redis(
                host=redis_config.get('host', 'localhost').split(':')[0],
                port=int(redis_config.get('host', 'localhost:6379').split(':')[1]),
                db=redis_config.get('db', 0),
                password=redis_config.get('password'),
                decode_responses=True
            )
        except Exception as e:
            logging.error(f"Redis连接失败: {e}")
    
    def _init_sms_client(self):
        """初始化阿里云短信客户端"""
        if not ALIYUN_SMS_AVAILABLE:
            logging.warning("阿里云短信SDK不可用")
            return
            
        try:
            from api.utils import get_base_config
            sms_config = get_base_config("sms", {})
            if not sms_config:
                logging.warning("短信配置不存在")
                return

            if not sms_config.get('access_key_id') or not sms_config.get('access_key_secret'):
                logging.warning("短信服务配置不完整，请检查配置文件")
                return

            config = open_api_models.Config(
                access_key_id=sms_config.get('access_key_id'),
                access_key_secret=sms_config.get('access_key_secret'),
                region_id=sms_config.get('region', 'cn-hangzhou')
            )
            config.endpoint = 'dysmsapi.aliyuncs.com'
            self.sms_client = DysmsapiClient(config)
            logging.info("阿里云短信客户端初始化成功")
        except Exception as e:
            logging.error(f"阿里云短信客户端初始化失败: {e}")
    
    def _generate_code(self, length: int = 6) -> str:
        """生成验证码"""
        return ''.join([str(random.randint(0, 9)) for _ in range(length)])
    
    def _get_redis_key(self, phone: str, key_type: str) -> str:
        """获取Redis键名"""
        return f"sms:{key_type}:{phone}"
    
    def _can_send_sms(self, phone: str) -> bool:
        """检查是否可以发送短信（频率限制）"""
        if not self.redis_client:
            return True
            
        last_send_key = self._get_redis_key(phone, "last_send")
        last_send_time = self.redis_client.get(last_send_key)
        
        if last_send_time:
            from api.utils import get_base_config
            sms_config = get_base_config("sms", {})
            interval = sms_config.get('send_interval', 60)
            if time.time() - float(last_send_time) < interval:
                return False
        
        return True
    
    def send_verification_code(self, phone: str) -> Dict[str, Any]:
        """发送验证码"""
        try:
            # 确保服务已初始化
            self._ensure_initialized()
            # 检查发送频率
            if not self._can_send_sms(phone):
                from api.utils import get_base_config
                sms_config = get_base_config("sms", {})
                return {
                    'success': False,
                    'message': f"请等待{sms_config.get('send_interval', 60)}秒后再试"
                }
            
            # 生成验证码
            from api.utils import get_base_config
            sms_config = get_base_config("sms", {})
            code_length = sms_config.get('code_length', 6)
            code = self._generate_code(code_length)
            
            # 如果短信客户端不可用，返回模拟成功（开发环境）
            if not self.sms_client:
                logging.warning(f"短信服务不可用，模拟发送验证码: {phone} -> {code}")
                # 在开发环境下，将验证码存储到Redis用于测试
                if self.redis_client:
                    code_key = self._get_redis_key(phone, "code")
                    expire_time = sms_config.get('code_expire', 300)
                    self.redis_client.setex(code_key, expire_time, code)
                
                return {
                    'success': True,
                    'message': '验证码发送成功',
                    'debug_code': code  # 仅开发环境返回
                }
            
            # 发送短信
            # sms_config已经在上面定义了
            request = dysmsapi_models.SendSmsRequest(
                phone_numbers=phone,
                sign_name=sms_config.get('sign_name'),
                template_code=sms_config.get('template_code'),
                template_param=json.dumps({'code': code})
            )

            # 打印请求详情
            logging.info(f"=== 阿里云短信请求详情 ===")
            logging.info(f"手机号: {phone}")
            logging.info(f"签名: {sms_config.get('sign_name')}")
            logging.info(f"模板代码: {sms_config.get('template_code')}")
            logging.info(f"模板参数: {json.dumps({'code': code})}")
            logging.info(f"验证码: {code}")

            runtime = util_models.RuntimeOptions()
            response = self.sms_client.send_sms_with_options(request, runtime)

            # 打印响应详情
            logging.info(f"=== 阿里云短信响应详情 ===")
            logging.info(f"响应状态码: {response.body.code}")
            logging.info(f"响应消息: {response.body.message}")
            logging.info(f"请求ID: {response.body.request_id}")
            logging.info(f"业务ID: {getattr(response.body, 'biz_id', 'N/A')}")
            logging.info(f"完整响应: {response.body}")
            print(f"[短信发送] 手机号: {phone}, 验证码: {code}, 状态: {response.body.code}, 消息: {response.body.message}")
            
            if response.body.code == 'OK':
                # 存储验证码到Redis
                if self.redis_client:
                    code_key = self._get_redis_key(phone, "code")
                    last_send_key = self._get_redis_key(phone, "last_send")
                    expire_time = sms_config.get('code_expire', 300)

                    self.redis_client.setex(code_key, expire_time, code)
                    self.redis_client.set(last_send_key, time.time())
                else:
                    # Redis不可用时存储到内存
                    self._last_codes[phone] = code

                return {
                    'success': True,
                    'message': '验证码发送成功'
                }
            elif response.body.code == 'isv.BUSINESS_LIMIT_CONTROL':
                # 流控限制，在开发环境下返回验证码用于测试
                logging.warning(f"触发阿里云流控限制，开发环境返回验证码用于测试: {code}")
                # 存储验证码到内存（用于验证）
                self._last_codes[phone] = code
                return {
                    'success': True,
                    'message': '验证码发送成功（开发模式）',
                    'debug_code': code,
                    'debug_info': '由于阿里云流控限制，当前为开发测试模式'
                }
            else:
                logging.error(f"短信发送失败: {response.body.message}")
                return {
                    'success': False,
                    'message': '验证码发送失败，请稍后重试'
                }
                
        except Exception as e:
            logging.exception(f"发送验证码异常: {e}")
            return {
                'success': False,
                'message': '验证码发送失败，请稍后重试'
            }
    
    def verify_code(self, phone: str, code: str) -> bool:
        """验证验证码"""
        try:
            logging.info(f"验证验证码: phone={phone}, code={code}")
            if not self.redis_client:
                logging.info("Redis不可用，使用内存验证")
                # 如果Redis不可用，在开发环境下允许特定验证码或最近发送的验证码
                if code == "123456":
                    logging.info("使用默认验证码123456验证成功")
                    return True
                # 检查是否是最近发送的验证码（存储在内存中）
                if hasattr(self, '_last_codes') and phone in self._last_codes:
                    stored_code = self._last_codes[phone]
                    logging.info(f"内存中存储的验证码: {stored_code}")
                    result = stored_code == code
                    logging.info(f"验证结果: {result}")
                    return result
                logging.info("内存中没有找到验证码")
                return False
            
            code_key = self._get_redis_key(phone, "code")
            stored_code = self.redis_client.get(code_key)
            
            if not stored_code:
                return False
            
            if stored_code == code:
                # 验证成功后删除验证码
                self.redis_client.delete(code_key)
                return True
            
            return False
            
        except Exception as e:
            logging.exception(f"验证码验证异常: {e}")
            return False
    
    def clear_verification_code(self, phone: str):
        """清除验证码（用于登录成功后清理）"""
        try:
            if self.redis_client:
                code_key = self._get_redis_key(phone, "code")
                self.redis_client.delete(code_key)
        except Exception as e:
            logging.exception(f"清除验证码异常: {e}")


# 全局短信服务实例
sms_service = SMSService()
