<!DOCTYPE html>
<html>
<head>
    <title>RAGFlow API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin:0;
            background: #fafafa;
        }
        .swagger-ui .topbar {
            background-color: #1976d2;
        }
        .swagger-ui .topbar .download-url-wrapper .select-label {
            color: #fff;
        }
        .swagger-ui .topbar .download-url-wrapper input[type=text] {
            border: 2px solid #547ac0;
        }
        .swagger-ui .info .title {
            color: #1976d2;
        }
        /* 增强认证区域样式 */
        .auth-container {
            background: #e3f2fd;
            border: 1px solid #1976d2;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .auth-title {
            color: #1976d2;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .auth-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin: 5px 0;
        }
        .auth-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
        }
        .auth-button:hover {
            background: #1565c0;
        }
        .auth-status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
        }
        .auth-success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .auth-error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    
    <!-- 自定义认证面板 -->
    <div id="auth-panel" class="auth-container" style="display: none;">
        <div class="auth-title">🔐 API认证 - 快速登录</div>
        <div>
            <input type="email" id="auth-email" class="auth-input" placeholder="邮箱地址" value="<EMAIL>">
            <input type="password" id="auth-password" class="auth-input" placeholder="密码" value="admin">
            <button id="auth-login" class="auth-button">登录获取Token</button>
            <button id="auth-clear" class="auth-button">清除认证</button>
        </div>
        <div id="auth-status"></div>
    </div>

    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            // 显示认证面板
            document.getElementById('auth-panel').style.display = 'block';
            
            const ui = SwaggerUIBundle({
                url: '/apispec.json',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
                defaultModelsExpandDepth: 2,
                defaultModelExpandDepth: 2,
                displayRequestDuration: true,
                docExpansion: 'list',
                filter: true,
                showExtensions: true,
                showCommonExtensions: true,
                persistAuthorization: true,
                requestInterceptor: function(request) {
                    // 自动添加认证头
                    const token = localStorage.getItem('ragflow_token');
                    if (token) {
                        request.headers['Authorization'] = 'Bearer ' + token;
                    }
                    return request;
                }
            });

            // 认证功能
            document.getElementById('auth-login').addEventListener('click', async function() {
                const email = document.getElementById('auth-email').value;
                const password = document.getElementById('auth-password').value;
                const statusDiv = document.getElementById('auth-status');
                
                try {
                    const response = await fetch('/v1/user/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password })
                    });
                    
                    const result = await response.json();
                    
                    if (result.code === 200 && result.data) {
                        const token = result.data.access_token;
                        localStorage.setItem('ragflow_token', token);
                        
                        // 设置Swagger UI的认证
                        ui.preauthorizeApiKey('BearerAuth', 'Bearer ' + token);
                        
                        statusDiv.className = 'auth-status auth-success';
                        statusDiv.textContent = '✅ 登录成功！Token已自动设置到所有API请求中。';
                    } else {
                        statusDiv.className = 'auth-status auth-error';
                        statusDiv.textContent = '❌ 登录失败: ' + (result.message || '未知错误');
                    }
                } catch (error) {
                    statusDiv.className = 'auth-status auth-error';
                    statusDiv.textContent = '❌ 网络错误: ' + error.message;
                }
            });
            
            document.getElementById('auth-clear').addEventListener('click', function() {
                localStorage.removeItem('ragflow_token');
                const statusDiv = document.getElementById('auth-status');
                statusDiv.className = 'auth-status';
                statusDiv.textContent = '🔓 认证已清除';
            });
            
            // 页面加载时检查是否已有token
            const existingToken = localStorage.getItem('ragflow_token');
            if (existingToken) {
                ui.preauthorizeApiKey('BearerAuth', 'Bearer ' + existingToken);
                document.getElementById('auth-status').className = 'auth-status auth-success';
                document.getElementById('auth-status').textContent = '✅ 已使用保存的Token';
            }
        };
    </script>
</body>
</html>
