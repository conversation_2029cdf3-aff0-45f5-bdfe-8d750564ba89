#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import re
import logging
from datetime import datetime, timedelta
from flask import request
from flask_login import login_user

from api import settings
from api.db import UserType
from api.db.services.user_service import UserService
from api.services.sms_service import sms_service
from api.utils import get_uuid, current_timestamp, datetime_format
from api.utils.api_utils import (
    construct_response,
    get_data_error_result,
    get_json_result,
    server_error_response,
    validate_request,
)

# 设置页面名称，确保路由正确注册
page_name = "sms-auth"


@manager.route("/send-code", methods=["POST"])  # noqa: F821
@validate_request("phone")
def send_verification_code():
    """
    发送短信验证码
    ---
    tags:
      - 2-短信登录
    parameters:
      - in: body
        name: body
        description: 手机号
        required: true
        schema:
          type: object
          properties:
            phone:
              type: string
              description: 手机号
    responses:
      200:
        description: 发送结果
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                success:
                  type: boolean
                  description: 是否发送成功
                message:
                  type: string
                  description: 结果消息
    """
    try:
        req = request.json
        phone = req["phone"].strip()
        
        # 验证手机号格式
        if not re.match(r"^1[3-9]\d{9}$", phone):
            return get_json_result(
                data=False,
                message="手机号格式有误",
                code=settings.RetCode.OPERATING_ERROR,
            )
        
        # 检查手机号是否在系统中（已授权）
        user = UserService.get_by_phone(phone)
        if not user:
            return get_json_result(
                data=False,
                message="该手机号未授权，无法登录",
                code=settings.RetCode.AUTHENTICATION_ERROR,
            )
        
        # 发送验证码
        result = sms_service.send_verification_code(phone)
        
        if result['success']:
            # 开发环境下返回验证码（仅用于测试）
            response_data = {}
            if 'debug_code' in result:
                response_data['debug_code'] = result['debug_code']

            return get_json_result(
                data=response_data,
                message=result['message']
            )
        else:
            return get_json_result(
                data=False,
                message=result['message'],
                code=settings.RetCode.OPERATING_ERROR,
            )
            
    except Exception as e:
        logging.exception(e)
        return server_error_response(e)


@manager.route("/login", methods=["POST"])  # noqa: F821
@validate_request("phone", "code")
def sms_login():
    """
    短信验证码登录
    ---
    tags:
      - 2-短信登录
    parameters:
      - in: body
        name: body
        description: 登录信息
        required: true
        schema:
          type: object
          properties:
            phone:
              type: string
              description: 手机号
            code:
              type: string
              description: 验证码
            remember_me:
              type: boolean
              description: 是否保持登录状态（30天）
    responses:
      200:
        description: 登录结果
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                access_token:
                  type: string
                  description: 访问令牌
                user_info:
                  type: object
                  description: 用户信息
    """
    try:
        req = request.json
        phone = req["phone"].strip()
        code = req["code"].strip()
        remember_me = req.get("remember_me", True)  # 默认保持登录状态
        
        # 验证手机号格式
        if not re.match(r"^1[3-9]\d{9}$", phone):
            return get_json_result(
                data=False,
                message="手机号格式有误",
                code=settings.RetCode.OPERATING_ERROR,
            )
        
        # 验证验证码格式
        if not re.match(r"^\d{6}$", code):
            return get_json_result(
                data=False,
                message="验证码格式有误",
                code=settings.RetCode.OPERATING_ERROR,
            )
        
        # 检查手机号是否在系统中
        user = UserService.get_by_phone(phone)
        if not user:
            return get_json_result(
                data=False,
                message="该手机号未授权，无法登录",
                code=settings.RetCode.AUTHENTICATION_ERROR,
            )
        
        # 验证验证码
        if not sms_service.verify_code(phone, code):
            return get_json_result(
                data=False,
                message="验证码输入有误，请重试",
                code=settings.RetCode.AUTHENTICATION_ERROR,
            )
        
        # 登录成功，重新获取用户对象
        user = UserService.get_by_phone(phone)
        
        # 生成新的访问令牌
        user.access_token = get_uuid()
        user.last_login_time = datetime.now()
        user.update_time = current_timestamp()
        user.update_date = datetime_format(datetime.now())

        # 设置token过期时间
        if remember_me:
            # 保持登录状态30天
            user.token_expire_time = datetime.now() + timedelta(days=30)
        else:
            # 不保持登录状态，设置较短的过期时间（1天）
            user.token_expire_time = datetime.now() + timedelta(days=1)
        
        # 保存用户信息
        user.save()
        
        # 执行登录
        login_user(user, remember=remember_me)
        
        # 清除验证码
        sms_service.clear_verification_code(phone)
        
        # 返回用户信息
        response_data = user.to_json()
        
        return construct_response(
            data=response_data,
            auth=user.get_id(),
            message="登录成功"
        )
        
    except Exception as e:
        logging.exception(e)
        return server_error_response(e)


@manager.route("/check-phone", methods=["POST"])  # noqa: F821
@validate_request("phone")
def check_phone_authorization():
    """
    检查手机号是否已授权
    ---
    tags:
      - 2-短信登录
    parameters:
      - in: body
        name: body
        description: 手机号
        required: true
        schema:
          type: object
          properties:
            phone:
              type: string
              description: 手机号
    responses:
      200:
        description: 检查结果
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                authorized:
                  type: boolean
                  description: 是否已授权
                user_info:
                  type: object
                  description: 用户基本信息（如果已授权）
    """
    try:
        req = request.json
        phone = req["phone"].strip()
        
        # 验证手机号格式
        if not re.match(r"^1[3-9]\d{9}$", phone):
            return get_json_result(
                data=False,
                message="手机号格式有误",
                code=settings.RetCode.OPERATING_ERROR,
            )
        
        # 检查手机号是否在系统中
        user = UserService.get_by_phone(phone)

        if user:
            return get_json_result(data={
                "authorized": True,
                "user_info": {
                    "nickname": user.nickname,
                    "is_superuser": user.is_superuser
                }
            })
        else:
            return get_json_result(data={
                "authorized": False,
                "message": "该手机号未授权"
            })
            
    except Exception as e:
        logging.exception(e)
        return server_error_response(e)


@manager.route("/logout", methods=["POST"])  # noqa: F821
def logout():
    """
    退出登录
    ---
    tags:
      - 2-短信登录
    parameters:
      - in: header
        name: Authorization
        type: string
        required: true
        description: 用户访问令牌
    responses:
      200:
        description: 退出登录结果
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 状态码，0表示成功
            data:
              type: boolean
              description: 是否成功退出
            message:
              type: string
              description: 响应消息
    """
    try:
        # 获取Authorization头
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            return get_json_result(
                data=False,
                message="未提供访问令牌",
                code=settings.RetCode.AUTHENTICATION_ERROR,
            )

        # 查找用户
        token = auth_header
        # 如果包含Bearer前缀，移除它
        if auth_header.startswith('Bearer '):
            token = auth_header[7:]

        user = UserService.get_by_access_token(token)
        if not user:
            return get_json_result(
                data=False,
                message="无效的访问令牌",
                code=settings.RetCode.AUTHENTICATION_ERROR,
            )

        # 清除用户的access_token，实现退出登录
        user.access_token = None
        user.token_expire_time = None
        user.update_time = current_timestamp()
        user.update_date = datetime_format(datetime.now())
        user.save()

        logging.info(f"用户 {user.nickname} ({user.phone}) 退出登录")

        return get_json_result(
            data=True,
            message="退出登录成功"
        )

    except Exception as e:
        logging.exception(e)
        return server_error_response(e)

