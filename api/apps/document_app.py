#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License
#
import json
import logging
import os.path
import pathlib
import re

import flask
from flask import request
from flask_login import current_user, login_required

from api import settings
from api.constants import FILE_NAME_LEN_LIMIT, IMG_BASE64_PREFIX
from api.db import VALID_FILE_TYPES, VALID_TASK_STATUS, FileSource, FileType, ParserType, TaskStatus, StatusEnum
from api.db.db_models import File, Task
from api.db.services import duplicate_name
from api.db.services.document_service import DocumentService, doc_upload_and_parse
from api.db.services.file2document_service import File2DocumentService
from api.db.services.file_service import FileService
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.services.task_service import TaskService, queue_tasks
from api.db.services.user_service import UserTenantService, UserService
from api.utils import get_uuid
from api.utils.api_utils import (
    get_data_error_result,
    get_json_result,
    server_error_response,
    validate_request,
)
from api.utils.file_utils import filename_type, get_project_base_directory, thumbnail
from api.utils.web_utils import html2pdf, is_valid_url
from api.db.init_data import get_default_knowledgebase_id
from deepdoc.parser.html_parser import RAGFlowHtmlParser
from rag.nlp import search
from rag.utils.storage_factory import STORAGE_IMPL


@manager.route("/upload", methods=["POST"])  # noqa: F821
def upload():
    """
    上传文档
    ---
    tags:
      - 3-知识库文档管理
    security:
      - ApiKeyAuth: []
    consumes:
      - multipart/form-data
    parameters:
      - in: formData
        name: file
        type: file
        required: true
        description: 上传的文档文件，支持多文件上传，支持word、excel、ppt、pdf、txt格式
    responses:
      200:
        description: 成功上传文档
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 状态码，0表示成功
            data:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    description: 文档ID
                  name:
                    type: string
                    description: 文件名
                  size:
                    type: integer
                    description: 文件大小
                  type:
                    type: string
                    description: 文件类型
                  progress:
                    type: number
                    description: 解析进度（0表示刚开始，-1表示失败）
                  run:
                    type: string
                    enum: ["0", "1", "2", "3", "4"]
                    description: |
                      运行状态：
                      - "0": 未开始
                      - "1": 运行中
                      - "2": 取消
                      - "3": 完成
                      - "4": 失败
                  progress_msg:
                    type: string
                    description: 进度消息
            message:
              type: string
              description: 返回消息
      401:
        description: 认证失败或权限不足
      400:
        description: 请求参数错误
      500:
        description: 服务器错误
    """
    # 权限检查：验证token有效性（与人员管理一致）
    auth_header = request.headers.get('Authorization')
    if not auth_header:
        return get_json_result(
            data=False,
            message="需要管理员权限",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    user = UserService.get_by_access_token(auth_header)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 使用默认知识库
    kb_id = get_default_knowledgebase_id()
    if not kb_id:
        return get_json_result(data=False, message='Default knowledgebase not found', code=settings.RetCode.SERVER_ERROR)

    if "file" not in request.files:
        return get_json_result(data=False, message="No file part!", code=settings.RetCode.ARGUMENT_ERROR)

    file_objs = request.files.getlist("file")
    for file_obj in file_objs:
        if file_obj.filename == "":
            return get_json_result(data=False, message="No file selected!", code=settings.RetCode.ARGUMENT_ERROR)
        if len(file_obj.filename.encode("utf-8")) > FILE_NAME_LEN_LIMIT:
            return get_json_result(data=False, message=f"File name must be {FILE_NAME_LEN_LIMIT} bytes or less.", code=settings.RetCode.ARGUMENT_ERROR)

        # 检查文件类型
        file_ext = file_obj.filename.split('.')[-1].lower() if '.' in file_obj.filename else ''
        if file_ext not in ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf', 'txt']:
            return get_json_result(data=False, message=f"Unsupported file type: {file_ext}. Only support word, excel, ppt, pdf, txt files.", code=settings.RetCode.ARGUMENT_ERROR)

    e, kb = KnowledgebaseService.get_by_id(kb_id)
    if not e:
        raise LookupError("Can't find this knowledgebase!")

    err, files = FileService.upload_document(kb, file_objs, user.id)

    if err:
        return get_json_result(data=files, message="\n".join(err), code=settings.RetCode.SERVER_ERROR)

    if not files:
        return get_json_result(data=files, message="There seems to be an issue with your file format. Please verify it is correct and not corrupted.", code=settings.RetCode.DATA_ERROR)

    uploaded_docs = [f[0] for f in files]  # remove the blob
    doc_ids = [doc.get('id') for doc in uploaded_docs if doc.get('id')]

    # 自动启动解析 - 简化权限检查，确保所有管理员都能解析
    if doc_ids:
        try:
            from api.db.services.task_service import queue_tasks
            from api.db.services.file2document_service import File2DocumentService
            import logging

            for doc_id in doc_ids:
                # 移除权限检查，因为既然能上传就应该能解析
                e, doc = DocumentService.get_by_id(doc_id)
                if e:
                    doc_dict = doc.to_dict()
                    # 获取知识库的tenant_id，而不是使用用户ID
                    e_kb, kb = KnowledgebaseService.get_by_id(doc.kb_id)
                    if e_kb:
                        doc_dict["tenant_id"] = kb.tenant_id
                    else:
                        doc_dict["tenant_id"] = user.id  # 备用方案

                    # 获取存储地址
                    bucket, name = File2DocumentService.get_storage_address(doc_id=doc_id)

                    # 创建解析任务并加入队列
                    queue_tasks(doc_dict, bucket, name, 0)
                    logging.info(f"Auto-started parsing for document: {doc_id} by user: {user.nickname}")
                else:
                    logging.error(f"Document not found: {doc_id}")
        except Exception as e:
            logging.error(f"Failed to auto-start parsing: {e}")
            import traceback
            logging.error(traceback.format_exc())

    return get_json_result(data=uploaded_docs, message="Documents uploaded and parsing started successfully")




@manager.route("/web_crawl", methods=["POST"])  # noqa: F821
@login_required
@validate_request("kb_id", "name", "url")
def web_crawl():
    kb_id = request.form.get("kb_id")
    if not kb_id:
        return get_json_result(data=False, message='Lack of "KB ID"', code=settings.RetCode.ARGUMENT_ERROR)
    name = request.form.get("name")
    url = request.form.get("url")
    if not is_valid_url(url):
        return get_json_result(data=False, message="The URL format is invalid", code=settings.RetCode.ARGUMENT_ERROR)
    e, kb = KnowledgebaseService.get_by_id(kb_id)
    if not e:
        raise LookupError("Can't find this knowledgebase!")

    blob = html2pdf(url)
    if not blob:
        return server_error_response(ValueError("Download failure."))

    root_folder = FileService.get_root_folder(current_user.id)
    pf_id = root_folder["id"]
    FileService.init_knowledgebase_docs(pf_id, current_user.id)
    kb_root_folder = FileService.get_kb_folder(current_user.id)
    kb_folder = FileService.new_a_file_from_kb(kb.tenant_id, kb.name, kb_root_folder["id"])

    try:
        filename = duplicate_name(DocumentService.query, name=name + ".pdf", kb_id=kb.id)
        filetype = filename_type(filename)
        if filetype == FileType.OTHER.value:
            raise RuntimeError("This type of file has not been supported yet!")

        location = filename
        while STORAGE_IMPL.obj_exist(kb_id, location):
            location += "_"
        STORAGE_IMPL.put(kb_id, location, blob)
        doc = {
            "id": get_uuid(),
            "kb_id": kb.id,
            "parser_id": kb.parser_id,
            "parser_config": kb.parser_config,
            "created_by": current_user.id,
            "type": filetype,
            "name": filename,
            "location": location,
            "size": len(blob),
            "thumbnail": thumbnail(filename, blob),
        }
        if doc["type"] == FileType.VISUAL:
            doc["parser_id"] = ParserType.PICTURE.value
        if doc["type"] == FileType.AURAL:
            doc["parser_id"] = ParserType.AUDIO.value
        if re.search(r"\.(ppt|pptx|pages)$", filename):
            doc["parser_id"] = ParserType.PRESENTATION.value
        if re.search(r"\.(eml)$", filename):
            doc["parser_id"] = ParserType.EMAIL.value
        DocumentService.insert(doc)
        FileService.add_file_from_kb(doc, kb_folder["id"], kb.tenant_id)
    except Exception as e:
        return server_error_response(e)
    return get_json_result(data=True)


@manager.route("/create", methods=["POST"])  # noqa: F821
@validate_request("name", "kb_id")
def create():
    # 权限检查：验证token有效性（与人员管理一致）
    auth_header = request.headers.get('Authorization')
    if not auth_header:
        return get_json_result(
            data=False,
            message="需要管理员权限",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    user = UserService.get_by_access_token(auth_header)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    req = request.get_json()
    kb_id = req["kb_id"]
    if not kb_id:
        return get_json_result(data=False, message='Lack of "KB ID"', code=settings.RetCode.ARGUMENT_ERROR)
    if len(req["name"].encode("utf-8")) > FILE_NAME_LEN_LIMIT:
        return get_json_result(data=False, message=f"File name must be {FILE_NAME_LEN_LIMIT} bytes or less.", code=settings.RetCode.ARGUMENT_ERROR)

    if req["name"].strip() == "":
        return get_json_result(data=False, message="File name can't be empty.", code=settings.RetCode.ARGUMENT_ERROR)
    req["name"] = req["name"].strip()

    try:
        e, kb = KnowledgebaseService.get_by_id(kb_id)
        if not e:
            return get_data_error_result(message="Can't find this knowledgebase!")

        if DocumentService.query(name=req["name"], kb_id=kb_id):
            return get_data_error_result(message="Duplicated document name in the same knowledgebase.")

        doc = DocumentService.insert(
            {
                "id": get_uuid(),
                "kb_id": kb.id,
                "parser_id": kb.parser_id,
                "parser_config": kb.parser_config,
                "created_by": user.id,
                "type": FileType.VIRTUAL,
                "name": req["name"],
                "location": "",
                "size": 0,
            }
        )
        return get_json_result(data=doc.to_json())
    except Exception as e:
        return server_error_response(e)


@manager.route("/list", methods=["POST"])  # noqa: F821
def list_docs():
    """
    获取文档列表

    用于获取默认知识库中的文档列表，支持分页、搜索和筛选。
    前端通过轮询此接口来获取文档解析进度。

    ---
    tags:
      - 3-知识库文档管理
    security:
      - ApiKeyAuth: []
    parameters:
      - in: body
        name: body
        required: true
        schema:
          type: object
          properties:
            pageNum:
              type: integer
              default: 1
              description: 页码
            pageSize:
              type: integer
              default: 10
              description: 每页数量
            filters:
              type: object
              properties:
                keyword:
                  type: string
                  description: 搜索关键词，支持文件名模糊搜索
                status:
                  type: string
                  enum: ["active", "inactive", "all"]
                  default: "all"
                  description: 文档状态筛选
                createdAtStart:
                  type: string
                  format: date
                  description: 创建时间开始日期
                createdAtEnd:
                  type: string
                  format: date
                  description: 创建时间结束日期
                fileType:
                  type: string
                  description: 文件类型筛选
                orderby:
                  type: string
                  default: "create_time"
                  description: 排序字段
                desc:
                  type: boolean
                  default: true
                  description: 是否降序排列
    responses:
      200:
        description: 成功获取文档列表
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 状态码，0表示成功
            data:
              type: object
              properties:
                total:
                  type: integer
                  description: 文档总数
                docs:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 文档ID，用于其他接口操作
                        example: "doc123456789"
                      name:
                        type: string
                        description: 文件名，超过20字符时前端应截断显示但保留扩展名
                        example: "销售培训资料.pdf"
                      size:
                        type: integer
                        description: 文件大小（字节），前端可转换为KB/MB显示
                        example: 1048576
                      type:
                        type: string
                        description: 文件类型扩展名
                        example: "pdf"
                      create_time:
                        type: string
                        description: 上传时间，格式为YYYY-MM-DD HH:mm:ss
                        example: "2025-01-14 10:30:00"
                      progress:
                        type: number
                        description: |
                          解析进度，取值说明：
                          - 0-1之间：正常解析进度，前端显示为百分比
                          - -1：解析失败
                          - 0：刚开始解析或未开始
                        example: 0.65
                      progress_msg:
                        type: string
                        description: |
                          进度消息，包含详细的解析状态信息：
                          - 解析中：显示具体进度信息
                          - 失败时：显示错误原因
                          - 队列中：显示排队信息
                        example: "Parsing document... 65% completed"
                      run:
                        type: string
                        enum: ["0", "1", "2", "3", "4"]
                        description: |
                          运行状态，前端根据此字段显示不同UI：
                          - "0": 未开始 - 显示"未解析"
                          - "1": 运行中 - 显示"解析中 x%..."
                          - "2": 取消 - 显示"已取消"
                          - "3": 完成 - 显示"解析成功"（绿色）
                          - "4": 失败 - 显示"解析失败"（红色）+ 重试按钮
                        example: "1"
                      status:
                        type: string
                        enum: ["0", "1"]
                        description: |
                          文档状态，控制文档是否被AI使用：
                          - "1": 启用 - 文档会被AI检索
                          - "0": 禁用 - 文档不会被AI检索
                        example: "1"
                      enabled:
                        type: boolean
                        description: |
                          是否启用的布尔值表示，等同于status=="1"
                          前端可用此字段控制开关组件状态
                        example: true
                      can_edit:
                        type: boolean
                        description: |
                          当前用户是否可编辑文档状态
                          - true: 显示可操作的开关
                          - false: 显示只读的开关（置灰）
                        example: true
                      can_delete:
                        type: boolean
                        description: |
                          当前用户是否可删除文档
                          - true: 显示删除按钮
                          - false: 隐藏删除按钮
                        example: true
                      kb_id:
                        type: string
                        description: 知识库ID（系统内部使用）
                      parser_id:
                        type: string
                        description: 解析器ID（系统内部使用）
                      location:
                        type: string
                        description: 文件存储位置（系统内部使用）
                      chunk_num:
                        type: integer
                        description: 文档切片数量
                      token_num:
                        type: integer
                        description: 文档token数量
                      update_time:
                        type: string
                        description: 最后更新时间
            message:
              type: string
              description: 返回消息
      401:
        description: 认证失败
      500:
        description: 服务器错误
    """
    # 权限检查：验证token有效性（与人员管理一致）
    auth_header = request.headers.get('Authorization')
    if not auth_header:
        return get_json_result(
            data=False,
            message="需要管理员权限",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    user = UserService.get_by_access_token(auth_header)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 使用默认知识库
    kb_id = get_default_knowledgebase_id()
    if not kb_id:
        return get_json_result(data=False, message='Default knowledgebase not found', code=settings.RetCode.SERVER_ERROR)

    # 从请求体获取参数
    req = request.get_json() or {}
    page_number = req.get("pageNum", 1)
    items_per_page = req.get("pageSize", 10)

    filters = req.get("filters", {})
    keywords = filters.get("keyword", "")
    status_filter = filters.get("status", "all")
    file_type = filters.get("fileType", "")
    orderby = filters.get("orderby", "create_time")
    desc = filters.get("desc", True)

    # 处理状态筛选
    run_status = []
    if status_filter == "active":
        run_status = ["1"]  # 只显示已启用的
    elif status_filter == "inactive":
        run_status = ["0"]  # 只显示已禁用的
    # status_filter == "all" 时不筛选状态

    # 处理文件类型筛选
    types = []
    if file_type:
        types = [file_type]

    try:
        docs, tol = DocumentService.get_by_kb_id(kb_id, page_number, items_per_page, orderby, desc, keywords, run_status, types)

        # 检查用户是否为管理员
        is_admin = user.is_superuser if hasattr(user, 'is_superuser') else False

        for doc_item in docs:
            if doc_item["thumbnail"] and not doc_item["thumbnail"].startswith(IMG_BASE64_PREFIX):
                doc_item["thumbnail"] = f"/v1/document/image/{kb_id}-{doc_item['thumbnail']}"

            # 添加启用状态字段（基于status字段：1=启用，0=禁用）
            doc_item["enabled"] = doc_item.get("status", "1") == "1"

            # 添加用户权限信息
            doc_item["can_edit"] = is_admin
            doc_item["can_delete"] = is_admin

        return get_json_result(data={"total": tol, "docs": docs})
    except Exception as e:
        return server_error_response(e)



@manager.route("/infos", methods=["POST"])  # noqa: F821
def docinfos():
    # Token验证
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        return get_json_result(
            data=False,
            message="缺少访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    token = auth_header
    # 如果包含Bearer前缀，移除它
    if auth_header.startswith('Bearer '):
        token = auth_header[7:]

    user = UserService.get_by_access_token(token)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 检查用户是否为管理员
    if not (hasattr(user, 'is_superuser') and user.is_superuser):
        return get_json_result(data=False, message='Only administrators can access document info', code=settings.RetCode.AUTHENTICATION_ERROR)
    req = request.json
    doc_ids = req["doc_ids"]
    # 简化权限检查：检查是否为默认知识库的文档
    from api.db.init_data import get_default_knowledgebase_id
    default_kb_id = get_default_knowledgebase_id()

    for doc_id in doc_ids:
        e, doc = DocumentService.get_by_id(doc_id)
        if not e:
            return get_json_result(data=False, message="Document not found.", code=settings.RetCode.DATA_ERROR)
        if doc.kb_id != default_kb_id:
            return get_json_result(data=False, message="Can only access documents in default knowledgebase.", code=settings.RetCode.AUTHENTICATION_ERROR)
    docs = DocumentService.get_by_ids(doc_ids)
    return get_json_result(data=list(docs.dicts()))


@manager.route("/thumbnails", methods=["GET"])  # noqa: F821
# @login_required
def thumbnails():
    doc_ids = request.args.get("doc_ids").split(",")
    if not doc_ids:
        return get_json_result(data=False, message='Lack of "Document ID"', code=settings.RetCode.ARGUMENT_ERROR)

    try:
        docs = DocumentService.get_thumbnails(doc_ids)

        for doc_item in docs:
            if doc_item["thumbnail"] and not doc_item["thumbnail"].startswith(IMG_BASE64_PREFIX):
                doc_item["thumbnail"] = f"/v1/document/image/{doc_item['kb_id']}-{doc_item['thumbnail']}"

        return get_json_result(data={d["id"]: d["thumbnail"] for d in docs})
    except Exception as e:
        return server_error_response(e)


@manager.route("/change_status", methods=["POST"])  # noqa: F821
@validate_request("doc_id", "status")
def change_status():
    """
    切换文档状态
    ---
    tags:
      - 3-知识库文档管理
    security:
      - ApiKeyAuth: []
    parameters:
      - in: body
        name: body
        required: true
        schema:
          type: object
          required:
            - doc_id
            - status
          properties:
            doc_id:
              type: string
              description: 文档ID
            status:
              type: string
              enum: ["0", "1"]
              description: 状态值，"1"表示启用，"0"表示禁用
    responses:
      200:
        description: 成功切换文档状态
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 状态码，0表示成功
            data:
              type: boolean
              description: 操作结果
            message:
              type: string
              description: 返回消息
      401:
        description: 认证失败或权限不足
      404:
        description: 文档不存在
      500:
        description: 服务器错误
    """
    # Token验证
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        return get_json_result(
            data=False,
            message="缺少访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    token = auth_header
    # 如果包含Bearer前缀，移除它
    if auth_header.startswith('Bearer '):
        token = auth_header[7:]

    user = UserService.get_by_access_token(token)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 检查用户是否为管理员
    if not (hasattr(user, 'is_superuser') and user.is_superuser):
        return get_json_result(data=False, message='Only administrators can change document status', code=settings.RetCode.AUTHENTICATION_ERROR)

    req = request.json
    if str(req["status"]) not in ["0", "1"]:
        return get_json_result(data=False, message='"Status" must be either 0 or 1!', code=settings.RetCode.ARGUMENT_ERROR)

    # 简化权限检查：检查是否为默认知识库的文档
    e, doc = DocumentService.get_by_id(req["doc_id"])
    if not e:
        return get_json_result(data=False, message="Document not found.", code=settings.RetCode.DATA_ERROR)

    from api.db.init_data import get_default_knowledgebase_id
    default_kb_id = get_default_knowledgebase_id()
    if doc.kb_id != default_kb_id:
        return get_json_result(data=False, message="Can only operate documents in default knowledgebase.", code=settings.RetCode.AUTHENTICATION_ERROR)

    try:
        e, doc = DocumentService.get_by_id(req["doc_id"])
        if not e:
            return get_data_error_result(message="Document not found!")
        e, kb = KnowledgebaseService.get_by_id(doc.kb_id)
        if not e:
            return get_data_error_result(message="Can't find this knowledgebase!")

        if not DocumentService.update_by_id(req["doc_id"], {"status": str(req["status"])}):
            return get_data_error_result(message="Database error (Document update)!")

        status = int(req["status"])
        settings.docStoreConn.update({"doc_id": req["doc_id"]}, {"available_int": status}, search.index_name(kb.tenant_id), doc.kb_id)
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/rm/<doc_id>", methods=["DELETE"])  # noqa: F821
def rm(doc_id):
    """
    删除文档
    ---
    tags:
      - 3-知识库文档管理
    security:
      - ApiKeyAuth: []
    parameters:
      - in: path
        name: doc_id
        type: string
        required: true
        description: 文档ID
    responses:
      200:
        description: 成功删除文档
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 状态码，0表示成功
            data:
              type: boolean
              description: 操作结果
            message:
              type: string
              description: 返回消息
      401:
        description: 认证失败或权限不足
      404:
        description: 文档不存在
      500:
        description: 服务器错误
    """
    # 权限检查：验证token有效性
    auth_header = request.headers.get('Authorization')
    if not auth_header:
        return get_json_result(
            data=False,
            message="需要管理员权限",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    user = UserService.get_by_access_token(auth_header)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # doc_id现在从URL路径参数获取
    doc_ids = [doc_id]

    # 简化权限检查：所有管理员都可以删除默认知识库中的文档
    for doc_id in doc_ids:
        # 检查文档是否存在
        e, doc = DocumentService.get_by_id(doc_id)
        if not e:
            return get_json_result(data=False, message="Document not found.", code=settings.RetCode.DATA_ERROR)

        # 检查是否为默认知识库的文档
        from api.db.init_data import get_default_knowledgebase_id
        default_kb_id = get_default_knowledgebase_id()
        if doc.kb_id != default_kb_id:
            return get_json_result(data=False, message="Can only delete documents in default knowledgebase.", code=settings.RetCode.AUTHENTICATION_ERROR)

    root_folder = FileService.get_root_folder(user.id)
    pf_id = root_folder["id"]
    FileService.init_knowledgebase_docs(pf_id, user.id)
    errors = ""
    kb_table_num_map = {}
    for doc_id in doc_ids:
        try:
            e, doc = DocumentService.get_by_id(doc_id)
            if not e:
                return get_data_error_result(message="Document not found!")
            tenant_id = DocumentService.get_tenant_id(doc_id)
            if not tenant_id:
                return get_data_error_result(message="Tenant not found!")

            b, n = File2DocumentService.get_storage_address(doc_id=doc_id)

            TaskService.filter_delete([Task.doc_id == doc_id])
            if not DocumentService.remove_document(doc, tenant_id):
                return get_data_error_result(message="Database error (Document removal)!")

            f2d = File2DocumentService.get_by_document_id(doc_id)
            deleted_file_count = 0
            if f2d:
                deleted_file_count = FileService.filter_delete([File.source_type == FileSource.KNOWLEDGEBASE, File.id == f2d[0].file_id])
            File2DocumentService.delete_by_document_id(doc_id)
            if deleted_file_count > 0:
                STORAGE_IMPL.rm(b, n)

            doc_parser = doc.parser_id
            if doc_parser == ParserType.TABLE:
                kb_id = doc.kb_id
                if kb_id not in kb_table_num_map:
                    counts = DocumentService.count_by_kb_id(kb_id=kb_id, keywords="", run_status=[TaskStatus.DONE], types=[])
                    kb_table_num_map[kb_id] = counts
                kb_table_num_map[kb_id] -= 1
                if kb_table_num_map[kb_id] <= 0:
                    KnowledgebaseService.delete_field_map(kb_id)
        except Exception as e:
            errors += str(e)

    if errors:
        return get_json_result(data=False, message=errors, code=settings.RetCode.SERVER_ERROR)

    return get_json_result(data=True)


@manager.route("/run", methods=["POST"])  # noqa: F821
@validate_request("doc_ids", "run")
def run():
    """
    启动/停止文档解析
    ---
    tags:
      - 3-知识库文档管理
    security:
      - ApiKeyAuth: []
    parameters:
      - in: body
        name: body
        required: true
        schema:
          type: object
          required:
            - doc_ids
            - run
          properties:
            doc_ids:
              type: array
              items:
                type: string
              description: 文档ID数组
            run:
              type: string
              enum: ["1", "2"]
              description: |
                运行控制操作：
                - "1": 启动解析 - 开始或重新开始文档解析
                - "2": 取消解析 - 停止当前解析任务
                注意：此字段用于控制解析操作，与文档状态查询中的run字段含义不同
            delete:
              type: boolean
              description: 是否删除已有解析数据
    responses:
      200:
        description: 成功启动/停止解析
        schema:
          type: object
          properties:
            code:
              type: integer
              description: 状态码，0表示成功
            data:
              type: boolean
              description: 操作结果
            message:
              type: string
              description: 返回消息
      401:
        description: 认证失败或权限不足
      404:
        description: 文档不存在
      500:
        description: 服务器错误
    """
    # 权限检查：验证token有效性
    auth_header = request.headers.get('Authorization')
    if not auth_header:
        return get_json_result(
            data=False,
            message="需要管理员权限",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    user = UserService.get_by_access_token(auth_header)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    req = request.get_json()
    # 简化权限检查：所有管理员都可以解析默认知识库中的文档
    for doc_id in req["doc_ids"]:
        # 检查文档是否存在
        e, doc = DocumentService.get_by_id(doc_id)
        if not e:
            return get_json_result(data=False, message="Document not found.", code=settings.RetCode.DATA_ERROR)

        # 检查是否为默认知识库的文档
        from api.db.init_data import get_default_knowledgebase_id
        default_kb_id = get_default_knowledgebase_id()
        if doc.kb_id != default_kb_id:
            return get_json_result(data=False, message="Can only operate documents in default knowledgebase.", code=settings.RetCode.AUTHENTICATION_ERROR)
    try:
        kb_table_num_map = {}
        for id in req["doc_ids"]:
            info = {"run": str(req["run"]), "progress": 0}
            if str(req["run"]) == TaskStatus.RUNNING.value and req.get("delete", False):
                info["progress_msg"] = ""
                info["chunk_num"] = 0
                info["token_num"] = 0

            e, doc = DocumentService.get_by_id(id)
            if not e:
                return get_data_error_result(message="Document not found!")
            if doc.run == TaskStatus.DONE.value:
                DocumentService.clear_chunk_num_when_rerun(doc.id)

            DocumentService.update_by_id(id, info)
            tenant_id = DocumentService.get_tenant_id(id)
            if not tenant_id:
                return get_data_error_result(message="Tenant not found!")
            e, doc = DocumentService.get_by_id(id)
            if not e:
                return get_data_error_result(message="Document not found!")
            if req.get("delete", False):
                TaskService.filter_delete([Task.doc_id == id])
                if settings.docStoreConn.indexExist(search.index_name(tenant_id), doc.kb_id):
                    settings.docStoreConn.delete({"doc_id": id}, search.index_name(tenant_id), doc.kb_id)

            if str(req["run"]) == TaskStatus.RUNNING.value:
                e, doc = DocumentService.get_by_id(id)
                doc = doc.to_dict()
                doc["tenant_id"] = tenant_id

                doc_parser = doc.get("parser_id", ParserType.NAIVE)
                if doc_parser == ParserType.TABLE:
                    kb_id = doc.get("kb_id")
                    if not kb_id:
                        continue
                    if kb_id not in kb_table_num_map:
                        count = DocumentService.count_by_kb_id(kb_id=kb_id, keywords="", run_status=[TaskStatus.DONE], types=[])
                        kb_table_num_map[kb_id] = count
                        if kb_table_num_map[kb_id] <= 0:
                            KnowledgebaseService.delete_field_map(kb_id)
                bucket, name = File2DocumentService.get_storage_address(doc_id=doc["id"])
                queue_tasks(doc, bucket, name, 0)

        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/rename", methods=["POST"])  # noqa: F821
@validate_request("doc_id", "name")
def rename():
    # Token验证
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        return get_json_result(
            data=False,
            message="缺少访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    token = auth_header
    # 如果包含Bearer前缀，移除它
    if auth_header.startswith('Bearer '):
        token = auth_header[7:]

    user = UserService.get_by_access_token(token)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 检查用户是否为管理员
    if not (hasattr(user, 'is_superuser') and user.is_superuser):
        return get_json_result(data=False, message='Only administrators can rename documents', code=settings.RetCode.AUTHENTICATION_ERROR)

    req = request.json
    # 简化权限检查：检查是否为默认知识库的文档
    e, doc = DocumentService.get_by_id(req["doc_id"])
    if not e:
        return get_json_result(data=False, message="Document not found.", code=settings.RetCode.DATA_ERROR)

    from api.db.init_data import get_default_knowledgebase_id
    default_kb_id = get_default_knowledgebase_id()
    if doc.kb_id != default_kb_id:
        return get_json_result(data=False, message="Can only rename documents in default knowledgebase.", code=settings.RetCode.AUTHENTICATION_ERROR)
    try:
        e, doc = DocumentService.get_by_id(req["doc_id"])
        if not e:
            return get_data_error_result(message="Document not found!")
        if pathlib.Path(req["name"].lower()).suffix != pathlib.Path(doc.name.lower()).suffix:
            return get_json_result(data=False, message="The extension of file can't be changed", code=settings.RetCode.ARGUMENT_ERROR)
        if len(req["name"].encode("utf-8")) > FILE_NAME_LEN_LIMIT:
            return get_json_result(data=False, message=f"File name must be {FILE_NAME_LEN_LIMIT} bytes or less.", code=settings.RetCode.ARGUMENT_ERROR)

        for d in DocumentService.query(name=req["name"], kb_id=doc.kb_id):
            if d.name == req["name"]:
                return get_data_error_result(message="Duplicated document name in the same knowledgebase.")

        if not DocumentService.update_by_id(req["doc_id"], {"name": req["name"]}):
            return get_data_error_result(message="Database error (Document rename)!")

        informs = File2DocumentService.get_by_document_id(req["doc_id"])
        if informs:
            e, file = FileService.get_by_id(informs[0].file_id)
            FileService.update_by_id(file.id, {"name": req["name"]})

        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/get/<doc_id>", methods=["GET"])  # noqa: F821
def get(doc_id):
    """
    预览文档 - 支持PDF直接预览，Word转换预览
    ---
    tags:
      - 3-知识库文档管理
    parameters:
      - in: path
        name: doc_id
        type: string
        required: true
        description: 文档ID
    produces:
      - application/pdf
      - application/msword
      - image/jpeg
      - image/png
      - text/plain
    responses:
      200:
        description: 成功返回文档内容
      404:
        description: 文档不存在
      500:
        description: 服务器错误
    """
    # 预览接口无需权限验证，允许公开访问

    try:
        e, doc = DocumentService.get_by_id(doc_id)
        if not e:
            return get_data_error_result(message="Document not found!")

        b, n = File2DocumentService.get_storage_address(doc_id=doc_id)
        file_data = STORAGE_IMPL.get(b, n)

        # 获取文件扩展名
        ext = re.search(r"\.([^.]+)$", doc.name)
        file_ext = ext.group(1).lower() if ext else ""

        # PDF文件直接预览
        if file_ext == "pdf":
            response = flask.make_response(file_data)
            response.headers.set("Content-Type", "application/pdf")
            # 修复中文文件名编码问题
            import urllib.parse
            safe_filename = urllib.parse.quote(doc.name.encode('utf-8'))
            response.headers.set("Content-Disposition", f"inline; filename*=UTF-8''{safe_filename}")
            return response

        # Office文档转换为PDF预览 (Word, Excel, PowerPoint)
        elif file_ext in ["doc", "docx", "xls", "xlsx", "ppt", "pptx"]:
            try:
                # 尝试转换Office文档为PDF
                import tempfile
                import os
                from subprocess import run, PIPE

                # 创建临时文件
                with tempfile.NamedTemporaryFile(suffix=f".{file_ext}", delete=False) as temp_input:
                    temp_input.write(file_data)
                    temp_input_path = temp_input.name

                temp_output_path = temp_input_path.replace(f".{file_ext}", ".pdf")

                # 使用LibreOffice转换，设置环境变量确保中文字体正确显示
                env = os.environ.copy()
                env['LANG'] = 'zh_CN.UTF-8'
                env['LC_ALL'] = 'zh_CN.UTF-8'

                result = run([
                    "libreoffice", "--headless", "--convert-to", "pdf",
                    "--outdir", os.path.dirname(temp_output_path),
                    temp_input_path
                ], capture_output=True, text=True, timeout=60, env=env)

                if os.path.exists(temp_output_path):
                    with open(temp_output_path, 'rb') as pdf_file:
                        pdf_data = pdf_file.read()

                    # 清理临时文件
                    os.unlink(temp_input_path)
                    os.unlink(temp_output_path)

                    response = flask.make_response(pdf_data)
                    response.headers.set("Content-Type", "application/pdf")
                    # 修复中文文件名编码问题
                    import urllib.parse
                    safe_filename = urllib.parse.quote(f"{doc.name}.pdf".encode('utf-8'))
                    response.headers.set("Content-Disposition", f"inline; filename*=UTF-8''{safe_filename}")
                    return response
                else:
                    # 转换失败，返回原文件下载
                    os.unlink(temp_input_path)
                    response = flask.make_response(file_data)
                    response.headers.set("Content-Type", "application/octet-stream")
                    # 修复中文文件名编码问题
                    import urllib.parse
                    safe_filename = urllib.parse.quote(doc.name.encode('utf-8'))
                    response.headers.set("Content-Disposition", f"attachment; filename*=UTF-8''{safe_filename}")
                    return response

            except Exception as convert_error:
                # 转换失败，返回原文件下载
                response = flask.make_response(file_data)
                response.headers.set("Content-Type", "application/octet-stream")
                # 修复中文文件名编码问题
                import urllib.parse
                safe_filename = urllib.parse.quote(doc.name.encode('utf-8'))
                response.headers.set("Content-Disposition", f"attachment; filename*=UTF-8''{safe_filename}")
                return response

        # 图片文件直接预览
        elif file_ext in ["jpg", "jpeg", "png", "gif", "bmp"]:
            response = flask.make_response(file_data)
            response.headers.set("Content-Type", f"image/{file_ext}")
            # 修复中文文件名编码问题
            import urllib.parse
            safe_filename = urllib.parse.quote(doc.name.encode('utf-8'))
            response.headers.set("Content-Disposition", f"inline; filename*=UTF-8''{safe_filename}")
            return response

        # 文本文件直接预览
        elif file_ext in ["txt", "md", "csv"]:
            response = flask.make_response(file_data)
            response.headers.set("Content-Type", "text/plain; charset=utf-8")
            # 修复中文文件名编码问题
            import urllib.parse
            safe_filename = urllib.parse.quote(doc.name.encode('utf-8'))
            response.headers.set("Content-Disposition", f"inline; filename*=UTF-8''{safe_filename}")
            return response

        # 其他文件类型，返回下载
        else:
            response = flask.make_response(file_data)
            response.headers.set("Content-Type", "application/octet-stream")
            # 修复中文文件名编码问题
            import urllib.parse
            safe_filename = urllib.parse.quote(doc.name.encode('utf-8'))
            response.headers.set("Content-Disposition", f"attachment; filename*=UTF-8''{safe_filename}")
            return response

    except Exception as e:
        return server_error_response(e)


@manager.route("/download/<doc_id>", methods=["GET"])  # noqa: F821
def download(doc_id):
    """
    下载文档
    ---
    tags:
      - 3-知识库文档管理
    security:
      - ApiKeyAuth: []
    parameters:
      - in: path
        name: doc_id
        type: string
        required: true
        description: 文档ID
    produces:
      - application/octet-stream
    responses:
      200:
        description: 成功返回文档文件
        headers:
          Content-Disposition:
            type: string
            description: 文件下载头信息
      401:
        description: 认证失败
      404:
        description: 文档不存在
      500:
        description: 服务器错误
    """
    # 权限检查已移除 - 允许公开下载
    """下载文档文件"""
    try:
        # 检查文档是否存在
        e, doc = DocumentService.get_by_id(doc_id)
        if not e:
            return get_data_error_result(message="Document not found!")

        # 文档访问权限检查已移除 - 允许公开下载

        # 获取文件存储地址
        b, n = File2DocumentService.get_storage_address(doc_id=doc_id)
        file_content = STORAGE_IMPL.get(b, n)

        # 创建响应
        response = flask.make_response(file_content)

        # 设置内容类型
        ext = re.search(r"\.([^.]+)$", doc.name)
        if ext:
            if doc.type == FileType.VISUAL.value:
                response.headers.set("Content-Type", "image/%s" % ext.group(1))
            else:
                response.headers.set("Content-Type", "application/%s" % ext.group(1))

        # 设置下载头信息 - 处理中文文件名编码
        import urllib.parse
        encoded_filename = urllib.parse.quote(doc.name.encode('utf-8'))
        response.headers.set("Content-Disposition", f"attachment; filename*=UTF-8''{encoded_filename}")

        return response
    except Exception as e:
        return server_error_response(e)


@manager.route("/change_parser", methods=["POST"])  # noqa: F821
@validate_request("doc_id", "parser_id")
def change_parser():
    # Token验证
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        return get_json_result(
            data=False,
            message="缺少访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    token = auth_header
    # 如果包含Bearer前缀，移除它
    if auth_header.startswith('Bearer '):
        token = auth_header[7:]

    user = UserService.get_by_access_token(token)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 检查用户是否为管理员
    if not (hasattr(user, 'is_superuser') and user.is_superuser):
        return get_json_result(data=False, message='Only administrators can change parser', code=settings.RetCode.AUTHENTICATION_ERROR)
    req = request.json

    # 简化权限检查：检查是否为默认知识库的文档
    e, doc = DocumentService.get_by_id(req["doc_id"])
    if not e:
        return get_json_result(data=False, message="Document not found.", code=settings.RetCode.DATA_ERROR)

    from api.db.init_data import get_default_knowledgebase_id
    default_kb_id = get_default_knowledgebase_id()
    if doc.kb_id != default_kb_id:
        return get_json_result(data=False, message="Can only change parser for documents in default knowledgebase.", code=settings.RetCode.AUTHENTICATION_ERROR)
    try:
        e, doc = DocumentService.get_by_id(req["doc_id"])
        if not e:
            return get_data_error_result(message="Document not found!")
        if doc.parser_id.lower() == req["parser_id"].lower():
            if "parser_config" in req:
                if req["parser_config"] == doc.parser_config:
                    return get_json_result(data=True)
            else:
                return get_json_result(data=True)

        if (doc.type == FileType.VISUAL and req["parser_id"] != "picture") or (re.search(r"\.(ppt|pptx|pages)$", doc.name) and req["parser_id"] != "presentation"):
            return get_data_error_result(message="Not supported yet!")

        e = DocumentService.update_by_id(doc.id, {"parser_id": req["parser_id"], "progress": 0, "progress_msg": "", "run": TaskStatus.UNSTART.value})
        if not e:
            return get_data_error_result(message="Document not found!")
        if "parser_config" in req:
            DocumentService.update_parser_config(doc.id, req["parser_config"])
        if doc.token_num > 0:
            e = DocumentService.increment_chunk_num(doc.id, doc.kb_id, doc.token_num * -1, doc.chunk_num * -1, doc.process_duation * -1)
            if not e:
                return get_data_error_result(message="Document not found!")
            tenant_id = DocumentService.get_tenant_id(req["doc_id"])
            if not tenant_id:
                return get_data_error_result(message="Tenant not found!")
            if settings.docStoreConn.indexExist(search.index_name(tenant_id), doc.kb_id):
                settings.docStoreConn.delete({"doc_id": doc.id}, search.index_name(tenant_id), doc.kb_id)

        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/image/<image_id>", methods=["GET"])  # noqa: F821
# @login_required
def get_image(image_id):
    try:
        arr = image_id.split("-")
        if len(arr) != 2:
            return get_data_error_result(message="Image not found.")
        bkt, nm = image_id.split("-")
        response = flask.make_response(STORAGE_IMPL.get(bkt, nm))
        response.headers.set("Content-Type", "image/JPEG")
        return response
    except Exception as e:
        return server_error_response(e)


@manager.route("/upload_and_parse", methods=["POST"])  # noqa: F821
@login_required
@validate_request("conversation_id")
def upload_and_parse():
    if "file" not in request.files:
        return get_json_result(data=False, message="No file part!", code=settings.RetCode.ARGUMENT_ERROR)

    file_objs = request.files.getlist("file")
    for file_obj in file_objs:
        if file_obj.filename == "":
            return get_json_result(data=False, message="No file selected!", code=settings.RetCode.ARGUMENT_ERROR)

    doc_ids = doc_upload_and_parse(request.form.get("conversation_id"), file_objs, current_user.id)

    return get_json_result(data=doc_ids)


@manager.route("/parse", methods=["POST"])  # noqa: F821
@login_required
def parse():
    url = request.json.get("url") if request.json else ""
    if url:
        if not is_valid_url(url):
            return get_json_result(data=False, message="The URL format is invalid", code=settings.RetCode.ARGUMENT_ERROR)
        download_path = os.path.join(get_project_base_directory(), "logs/downloads")
        os.makedirs(download_path, exist_ok=True)
        from seleniumwire.webdriver import Chrome, ChromeOptions

        options = ChromeOptions()
        options.add_argument("--headless")
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_experimental_option("prefs", {"download.default_directory": download_path, "download.prompt_for_download": False, "download.directory_upgrade": True, "safebrowsing.enabled": True})
        driver = Chrome(options=options)
        driver.get(url)
        res_headers = [r.response.headers for r in driver.requests if r and r.response]
        if len(res_headers) > 1:
            sections = RAGFlowHtmlParser().parser_txt(driver.page_source)
            driver.quit()
            return get_json_result(data="\n".join(sections))

        class File:
            filename: str
            filepath: str

            def __init__(self, filename, filepath):
                self.filename = filename
                self.filepath = filepath

            def read(self):
                with open(self.filepath, "rb") as f:
                    return f.read()

        r = re.search(r"filename=\"([^\"]+)\"", str(res_headers))
        if not r or not r.group(1):
            return get_json_result(data=False, message="Can't not identify downloaded file", code=settings.RetCode.ARGUMENT_ERROR)
        f = File(r.group(1), os.path.join(download_path, r.group(1)))
        txt = FileService.parse_docs([f], current_user.id)
        return get_json_result(data=txt)

    if "file" not in request.files:
        return get_json_result(data=False, message="No file part!", code=settings.RetCode.ARGUMENT_ERROR)

    file_objs = request.files.getlist("file")
    txt = FileService.parse_docs(file_objs, current_user.id)

    return get_json_result(data=txt)


@manager.route("/set_meta", methods=["POST"])  # noqa: F821
@validate_request("doc_id", "meta")
def set_meta():
    # Token验证
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        return get_json_result(
            data=False,
            message="缺少访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 验证token是否有效
    token = auth_header
    # 如果包含Bearer前缀，移除它
    if auth_header.startswith('Bearer '):
        token = auth_header[7:]

    user = UserService.get_by_access_token(token)
    if not user or not user.access_token:
        return get_json_result(
            data=False,
            message="无效的访问令牌",
            code=settings.RetCode.AUTHENTICATION_ERROR,
        )

    # 检查用户是否为管理员
    if not (hasattr(user, 'is_superuser') and user.is_superuser):
        return get_json_result(data=False, message='Only administrators can set meta', code=settings.RetCode.AUTHENTICATION_ERROR)
    req = request.json
    # 简化权限检查：检查是否为默认知识库的文档
    e, doc = DocumentService.get_by_id(req["doc_id"])
    if not e:
        return get_json_result(data=False, message="Document not found.", code=settings.RetCode.DATA_ERROR)

    from api.db.init_data import get_default_knowledgebase_id
    default_kb_id = get_default_knowledgebase_id()
    if doc.kb_id != default_kb_id:
        return get_json_result(data=False, message="Can only set meta for documents in default knowledgebase.", code=settings.RetCode.AUTHENTICATION_ERROR)
    try:
        meta = json.loads(req["meta"])
    except Exception as e:
        return get_json_result(data=False, message=f"Json syntax error: {e}", code=settings.RetCode.ARGUMENT_ERROR)
    if not isinstance(meta, dict):
        return get_json_result(data=False, message='Meta data should be in Json map format, like {"key": "value"}', code=settings.RetCode.ARGUMENT_ERROR)

    try:
        e, doc = DocumentService.get_by_id(req["doc_id"])
        if not e:
            return get_data_error_result(message="Document not found!")

        if not DocumentService.update_by_id(req["doc_id"], {"meta_fields": meta}):
            return get_data_error_result(message="Database error (meta updates)!")

        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)
