#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import re
import logging
from flask import request
from flask_login import login_required, current_user

from api import settings
from api.db import UserType
from api.db.services.user_service import UserService
from api.utils.api_utils import (
    construct_response,
    get_data_error_result,
    get_json_result,
    server_error_response,
    validate_request,
)







@manager.route("/list", methods=["GET"])  # noqa: F821
def get_staff_list():
    """
    获取人员列表
    ---
    tags:
      - 1-人员管理
    security:
      - ApiKeyAuth: []
    parameters:
      - in: query
        name: page
        type: integer
        description: 页码，默认为1
      - in: query
        name: page_size
        type: integer
        description: 每页数量，默认为10
    responses:
      200:
        description: 获取成功
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                users:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                      nickname:
                        type: string
                        description: 姓名
                      phone:
                        type: string
                        description: 手机号
                      create_time:
                        type: string
                        description: 添加时间
                      is_superuser:
                        type: boolean
                        description: 是否为超级管理员
                total:
                  type: integer
    """
    try:
        # 权限检查：验证token有效性
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            return get_json_result(
                data=False,
                message="需要管理员权限",
                code=settings.RetCode.AUTHENTICATION_ERROR,
            )

        # 验证token是否有效
        token = auth_header
        # 如果包含Bearer前缀，移除它
        if auth_header.startswith('Bearer '):
            token = auth_header[7:]

        user = UserService.get_by_access_token(token)
        if not user or not user.access_token:
            return get_json_result(
                data=False,
                message="无效的访问令牌",
                code=settings.RetCode.AUTHENTICATION_ERROR,
            )

        page = int(request.args.get("page", 1))
        page_size = int(request.args.get("page_size", 10))

        # 获取所有用户，按创建时间倒序排列（最新的在前面）
        users, total = UserService.get_all_users_ordered(page=page, page_size=page_size)

        # 格式化返回数据，只返回需要的字段
        formatted_users = []
        for user in users:
            formatted_users.append({
                "id": user.get("id"),
                "nickname": user.get("nickname"),
                "phone": user.get("phone"),
                "create_time": user.get("create_time"),
                "is_superuser": user.get("is_superuser", False)
            })

        return get_json_result(data={"users": formatted_users, "total": total})
    except Exception as e:
        logging.exception(e)
        return server_error_response(e)


@manager.route("/add", methods=["POST"])  # noqa: F821
@validate_request("nickname", "phone")
def add_staff():
    """
    添加人员
    ---
    tags:
      - 1-人员管理
    security:
      - ApiKeyAuth: []
    parameters:
      - in: body
        name: body
        description: 人员信息
        required: true
        schema:
          type: object
          properties:
            nickname:
              type: string
              description: 姓名
            phone:
              type: string
              description: 手机号
    responses:
      200:
        description: 添加成功
        schema:
          type: object
          properties:
            data:
              type: object
              properties:
                user_id:
                  type: string
                  description: 新用户ID
    """
    try:
        # 权限检查：验证token有效性
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            return get_json_result(
                data=False,
                message="需要管理员权限",
                code=settings.RetCode.AUTHENTICATION_ERROR,
            )

        # 验证token是否有效
        user = UserService.get_by_access_token(auth_header)
        if not user or not user.access_token:
            return get_json_result(
                data=False,
                message="无效的访问令牌",
                code=settings.RetCode.AUTHENTICATION_ERROR,
            )

        req = request.json
        nickname = req["nickname"].strip()
        phone = req["phone"].strip()
        
        # 验证手机号格式
        if not re.match(r"^1[3-9]\d{9}$", phone):
            return get_json_result(
                data=False,
                message="手机号格式有误",
                code=settings.RetCode.OPERATING_ERROR,
            )
        
        # 检查手机号是否已存在
        if UserService.phone_exists(phone):
            return get_json_result(
                data=False,
                message="手机号已存在",
                code=settings.RetCode.OPERATING_ERROR,
            )
        
        # 创建用户
        user_id = UserService.create_user_by_admin(
            nickname=nickname,
            phone=phone,
            is_superuser=False
        )
        
        return get_json_result(
            data={"user_id": user_id},
            message=f"成功添加用户 {nickname}"
        )
        
    except Exception as e:
        logging.exception(e)
        return server_error_response(e)


@manager.route("/delete/<user_id>", methods=["DELETE"])  # noqa: F821
def delete_staff(user_id):
    """
    删除人员
    ---
    tags:
      - 1-人员管理
    security:
      - ApiKeyAuth: []
    parameters:
      - in: path
        name: user_id
        type: string
        required: true
        description: 用户ID
    responses:
      200:
        description: 删除成功
        schema:
          type: object
          properties:
            data:
              type: boolean
    """
    try:
        # 权限检查：验证token有效性
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            return get_json_result(
                data=False,
                message="需要管理员权限",
                code=settings.RetCode.AUTHENTICATION_ERROR,
            )

        # 验证token是否有效
        current_user = UserService.get_by_access_token(auth_header)
        if not current_user or not current_user.access_token:
            return get_json_result(
                data=False,
                message="无效的访问令牌",
                code=settings.RetCode.AUTHENTICATION_ERROR,
            )

        # 检查用户是否存在
        user = UserService.filter_by_id(user_id)
        if not user:
            return get_data_error_result(message="用户不存在")
        
        # 不能删除管理员
        if user.is_superuser:
            return get_json_result(
                data=False,
                message="不能删除管理员",
                code=settings.RetCode.OPERATING_ERROR,
            )
        
        # 不能删除自己
        if user_id == current_user.id:
            return get_json_result(
                data=False,
                message="不能删除自己",
                code=settings.RetCode.OPERATING_ERROR,
            )
        
        # 执行删除
        UserService.delete_user_by_admin(user_id)
        
        return get_json_result(
            data=True,
            message=f"成功删除用户 {user.nickname}"
        )
        
    except Exception as e:
        logging.exception(e)
        return server_error_response(e)



