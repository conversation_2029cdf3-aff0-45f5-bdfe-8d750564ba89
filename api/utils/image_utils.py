#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import base64
import io
import logging
from PIL import Image


def compress_image(image_data, max_size_mb=200, quality=85, max_dimension=2048):
    """
    智能压缩图片到指定大小
    
    Args:
        image_data: 图片二进制数据
        max_size_mb: 最大文件大小(MB)，默认200MB
        quality: JPEG质量(1-100)，默认85
        max_dimension: 最大尺寸(像素)，默认2048
    
    Returns:
        压缩后的图片二进制数据
    """
    try:
        # 打开图片
        img = Image.open(io.BytesIO(image_data))
        
        # 转换为RGB模式(如果是RGBA等其他模式)
        if img.mode in ('RGBA', 'LA', 'P'):
            # 创建白色背景
            background = Image.new('RGB', img.size, (255, 255, 255))
            if img.mode == 'P':
                img = img.convert('RGBA')
            background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
            img = background
        elif img.mode != 'RGB':
            img = img.convert('RGB')
        
        # 获取原始尺寸
        original_width, original_height = img.size
        max_size_bytes = max_size_mb * 1024 * 1024
        
        # 如果原图已经小于限制且尺寸合适，直接返回
        if len(image_data) <= max_size_bytes and max(original_width, original_height) <= max_dimension:
            return image_data
        
        # 计算新尺寸
        if max(original_width, original_height) > max_dimension:
            # 按比例缩放到最大尺寸
            ratio = max_dimension / max(original_width, original_height)
            new_width = int(original_width * ratio)
            new_height = int(original_height * ratio)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 如果文件大小仍然超过限制，进一步压缩
        if len(image_data) > max_size_bytes:
            # 计算压缩比例
            compression_ratio = (max_size_bytes / len(image_data)) ** 0.5
            current_width, current_height = img.size
            new_width = int(current_width * compression_ratio)
            new_height = int(current_height * compression_ratio)
            
            # 确保最小尺寸
            if new_width < 64 or new_height < 64:
                new_width = max(64, new_width)
                new_height = max(64, new_height)
            
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 保存为JPEG格式
        output = io.BytesIO()
        img.save(output, format='JPEG', quality=quality, optimize=True)
        compressed_data = output.getvalue()
        
        # 如果压缩后仍然太大，降低质量
        attempts = 0
        while len(compressed_data) > max_size_bytes and quality > 10 and attempts < 5:
            quality = max(10, quality - 15)
            output = io.BytesIO()
            img.save(output, format='JPEG', quality=quality, optimize=True)
            compressed_data = output.getvalue()
            attempts += 1
        
        logging.info(f"图片压缩完成: {len(image_data)} -> {len(compressed_data)} bytes, "
                    f"尺寸: {original_width}x{original_height} -> {img.size[0]}x{img.size[1]}, "
                    f"质量: {quality}")
        
        return compressed_data
        
    except Exception as e:
        logging.error(f"图片压缩失败: {str(e)}")
        raise Exception(f"图片压缩失败: {str(e)}")


def image_to_base64(image_data):
    """将图片数据转换为base64编码"""
    return base64.b64encode(image_data).decode('utf-8')


def base64_to_image(base64_str):
    """将base64编码转换为图片数据"""
    if base64_str.startswith('data:image'):
        # 移除data URL前缀
        base64_str = base64_str.split(',', 1)[1]
    return base64.b64decode(base64_str)


def get_image_info(image_data):
    """
    获取图片基本信息
    
    Args:
        image_data: 图片二进制数据
    
    Returns:
        dict: 包含图片信息的字典
    """
    try:
        img = Image.open(io.BytesIO(image_data))
        return {
            "format": img.format,
            "mode": img.mode,
            "size": img.size,
            "width": img.size[0],
            "height": img.size[1],
            "file_size": len(image_data)
        }
    except Exception as e:
        logging.error(f"获取图片信息失败: {str(e)}")
        return None


def validate_image(image_data, max_size_mb=200, allowed_formats=None):
    """
    验证图片是否符合要求
    
    Args:
        image_data: 图片二进制数据
        max_size_mb: 最大文件大小(MB)
        allowed_formats: 允许的图片格式列表
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if allowed_formats is None:
        allowed_formats = ['JPEG', 'PNG', 'GIF', 'BMP', 'TIFF', 'WEBP']
    
    try:
        # 检查文件大小
        if len(image_data) > max_size_mb * 1024 * 1024:
            return False, f"图片文件过大，最大支持{max_size_mb}MB"
        
        # 检查是否为有效图片
        img = Image.open(io.BytesIO(image_data))
        
        # 检查格式
        if img.format not in allowed_formats:
            return False, f"不支持的图片格式: {img.format}，支持的格式: {', '.join(allowed_formats)}"
        
        # 检查尺寸
        width, height = img.size
        if width < 1 or height < 1:
            return False, "图片尺寸无效"
        
        if width > 10000 or height > 10000:
            return False, "图片尺寸过大，最大支持10000x10000像素"
        
        return True, ""
        
    except Exception as e:
        return False, f"图片格式错误: {str(e)}"


def create_thumbnail(image_data, size=(128, 128), quality=85):
    """
    创建图片缩略图
    
    Args:
        image_data: 图片二进制数据
        size: 缩略图尺寸，默认(128, 128)
        quality: JPEG质量
    
    Returns:
        缩略图二进制数据
    """
    try:
        img = Image.open(io.BytesIO(image_data))
        
        # 转换为RGB模式
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        # 创建缩略图
        img.thumbnail(size, Image.Resampling.LANCZOS)
        
        # 保存为JPEG
        output = io.BytesIO()
        img.save(output, format='JPEG', quality=quality, optimize=True)
        
        return output.getvalue()
        
    except Exception as e:
        logging.error(f"创建缩略图失败: {str(e)}")
        raise Exception(f"创建缩略图失败: {str(e)}")


def optimize_image_for_vision_model(image_data, target_size_mb=10, min_dimension=512, max_dimension=2048):
    """
    为视觉模型优化图片
    
    Args:
        image_data: 图片二进制数据
        target_size_mb: 目标文件大小(MB)
        min_dimension: 最小尺寸
        max_dimension: 最大尺寸
    
    Returns:
        优化后的图片二进制数据
    """
    try:
        img = Image.open(io.BytesIO(image_data))
        
        # 转换为RGB模式
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        width, height = img.size
        
        # 计算最佳尺寸
        if max(width, height) > max_dimension:
            # 缩小到最大尺寸
            ratio = max_dimension / max(width, height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)
        elif min(width, height) < min_dimension:
            # 放大到最小尺寸
            ratio = min_dimension / min(width, height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)
        else:
            new_width, new_height = width, height
        
        # 调整尺寸
        if (new_width, new_height) != (width, height):
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 压缩到目标大小
        quality = 95
        output = io.BytesIO()
        img.save(output, format='JPEG', quality=quality, optimize=True)
        compressed_data = output.getvalue()
        
        target_size_bytes = target_size_mb * 1024 * 1024
        while len(compressed_data) > target_size_bytes and quality > 30:
            quality -= 10
            output = io.BytesIO()
            img.save(output, format='JPEG', quality=quality, optimize=True)
            compressed_data = output.getvalue()
        
        logging.info(f"图片优化完成: {len(image_data)} -> {len(compressed_data)} bytes, "
                    f"尺寸: {width}x{height} -> {new_width}x{new_height}, 质量: {quality}")
        
        return compressed_data
        
    except Exception as e:
        logging.error(f"图片优化失败: {str(e)}")
        raise Exception(f"图片优化失败: {str(e)}")
