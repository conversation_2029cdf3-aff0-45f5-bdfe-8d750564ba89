#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
from api.db.db_models import FAQ
from api.db.services.common_service import CommonService
from api.utils import get_uuid


class FAQService(CommonService):
    model = FAQ

    @classmethod
    def get_by_keywords(cls, keywords, limit=10):
        """根据关键词搜索FAQ"""
        if not keywords:
            return []
        
        # 构建搜索条件
        query = cls.model.select().where(cls.model.status == "1")
        
        # 分词搜索
        keyword_list = [k.strip() for k in keywords.split() if k.strip()]
        if keyword_list:
            conditions = []
            for keyword in keyword_list:
                # 在问题、答案、关键词字段中搜索
                condition = (
                    cls.model.question.contains(keyword) |
                    cls.model.answer.contains(keyword) |
                    cls.model.keywords.contains(keyword)
                )
                conditions.append(condition)
            
            # 使用OR连接所有条件
            if conditions:
                from functools import reduce
                import operator
                final_condition = reduce(operator.or_, conditions)
                query = query.where(final_condition)
        
        # 按排序顺序和ID排序
        query = query.order_by(cls.model.sort_order.asc(), cls.model.id.asc())
        
        if limit:
            query = query.limit(limit)
        
        return list(query)

    @classmethod
    def search_faq(cls, question_text, limit=5):
        """智能搜索FAQ，返回最匹配的结果"""
        if not question_text:
            return []
        
        # 提取关键词（简单的中文分词）
        keywords = cls._extract_keywords(question_text)
        
        # 根据关键词搜索
        results = cls.get_by_keywords(" ".join(keywords), limit)
        
        # 如果没有结果，尝试模糊搜索
        if not results:
            results = cls._fuzzy_search(question_text, limit)
        
        return results

    @classmethod
    def _extract_keywords(cls, text):
        """提取关键词"""
        # 移除标点符号，保留中文、英文、数字
        clean_text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
        
        # 分词（简单按空格分割）
        words = [w.strip() for w in clean_text.split() if w.strip() and len(w.strip()) > 1]
        
        return words

    @classmethod
    def _fuzzy_search(cls, question_text, limit=5):
        """模糊搜索"""
        query = cls.model.select().where(cls.model.status == "1")
        
        # 使用LIKE进行模糊搜索
        condition = (
            cls.model.question.contains(question_text) |
            cls.model.answer.contains(question_text)
        )
        
        query = query.where(condition)
        query = query.order_by(cls.model.sort_order.asc(), cls.model.id.asc())
        
        if limit:
            query = query.limit(limit)
        
        return list(query)

    @classmethod
    def get_all_active(cls, page=1, page_size=20):
        """获取所有有效的FAQ，支持分页"""
        offset = (page - 1) * page_size
        
        query = cls.model.select().where(cls.model.status == "1")
        query = query.order_by(cls.model.sort_order.asc(), cls.model.id.asc())
        query = query.offset(offset).limit(page_size)
        
        return list(query)

    @classmethod
    def count_active(cls):
        """统计有效FAQ数量"""
        return cls.model.select().where(cls.model.status == "1").count()

    @classmethod
    def create_faq(cls, question, answer, frequency="高频问题", category=None, keywords=None, sort_order=0):
        """创建新的FAQ"""
        faq_id = get_uuid()

        faq_data = {
            "id": faq_id,
            "question": question,
            "answer": answer,
            "frequency": frequency,
            "category": category,
            "keywords": keywords,
            "sort_order": sort_order,
            "status": "1"
        }

        # 创建FAQ对象并保存
        faq = cls.model(**faq_data)
        faq.save(force_insert=True)
        return faq

    @classmethod
    def batch_create_faqs(cls, faq_list):
        """批量创建FAQ"""
        created_faqs = []
        
        for faq_data in faq_list:
            try:
                faq = cls.create_faq(
                    question=faq_data.get("question"),
                    answer=faq_data.get("answer"),
                    frequency=faq_data.get("frequency", "高频问题"),
                    category=faq_data.get("category"),
                    keywords=faq_data.get("keywords"),
                    sort_order=faq_data.get("sort_order", 0)
                )
                created_faqs.append(faq)
            except Exception as e:
                print(f"创建FAQ失败: {e}")
                continue
        
        return created_faqs
