#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
import logging
import base64
import json
import os
import time
import uuid
from copy import deepcopy

from api.db import LLMType, UserTenantRole
from api.db.db_models import init_database_tables as init_web_db, LLMFactories, LLM, TenantLLM
from api.db.services import UserService
from api.db.services.canvas_service import CanvasTemplateService
from api.db.services.document_service import DocumentService
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.services.llm_service import LLMFactoriesService, LLMService, TenantLLMService, LLMBundle
from api.db.services.user_service import TenantService, UserTenantService
from api import settings
from api.utils.file_utils import get_project_base_directory


def encode_to_base64(input_string):
    base64_encoded = base64.b64encode(input_string.encode('utf-8'))
    return base64_encoded.decode('utf-8')


def create_default_knowledgebase(tenant_id, embd_id):
    """创建默认知识库：经略销售助手"""
    try:
        # 检查是否已存在默认知识库
        existing_kb = KnowledgebaseService.query(tenant_id=tenant_id, name="经略销售助手")
        if existing_kb:
            logging.info("Default knowledgebase '经略销售助手' already exists.")
            return existing_kb[0].id

        # 创建默认知识库
        default_kb = {
            "id": str(uuid.uuid4()).replace("-", ""),
            "name": "经略销售助手",
            "tenant_id": tenant_id,
            "description": "默认知识库，用于存储销售助手相关文档",
            "language": "Chinese",
            "embd_id": embd_id,
            "permission": "me",
            "created_by": tenant_id,
            "doc_num": 0,
            "token_num": 0,
            "chunk_num": 0,
            "similarity_threshold": 0.2,
            "vector_similarity_weight": 0.3,
            "parser_id": "naive",
            "parser_config": {"pages": [[1, 1000000]]},
            "status": "1"
        }

        if KnowledgebaseService.save(**default_kb):
            logging.info(f"Default knowledgebase '经略销售助手' created successfully with ID: {default_kb['id']}")
            return default_kb['id']
        else:
            logging.error("Failed to create default knowledgebase.")
            return None
    except Exception as e:
        logging.error(f"Error creating default knowledgebase: {e}")
        return None


def get_default_knowledgebase_id():
    """获取默认知识库ID"""
    try:
        # 查找名为"经略销售助手"的知识库
        kbs = KnowledgebaseService.query(name="经略销售助手")
        if kbs:
            return kbs[0].id
        else:
            logging.warning("Default knowledgebase '经略销售助手' not found.")
            return None
    except Exception as e:
        logging.error(f"Error getting default knowledgebase ID: {e}")
        return None


def create_default_dialog(tenant_id, kb_id):
    """创建默认助手：经略销售助手"""
    from api.db.services.dialog_service import DialogService
    from api.utils import get_uuid

    try:
        # 检查是否已存在默认助手
        existing_dialog = DialogService.query(tenant_id=tenant_id, name="经略销售助手")
        if existing_dialog:
            logging.info("Default dialog '经略销售助手' already exists.")
            return existing_dialog[0].id

        # 创建默认助手
        default_dialog = {
            "id": get_uuid(),
            "name": "经略销售助手",
            "tenant_id": tenant_id,
            "description": "默认销售助手，具备图片理解和知识库问答能力",
            "kb_ids": [kb_id] if kb_id else [],
            "llm_id": "qwen-plus",  # 使用阿里云Qwen模型
            "llm_setting": {
                "temperature": 0.7,
                "top_p": 0.9,
                "presence_penalty": 0.4,
                "frequency_penalty": 0.7,
                "max_tokens": 2048
            },
            "prompt_config": {
                "system": "你是经略销售助手，一个专业的销售顾问AI。你具备以下能力：\n1. 理解和分析图片内容\n2. 基于知识库回答产品相关问题\n3. 提供专业的销售建议\n4. 保持友好和专业的沟通风格\n\n请始终以客户为中心，提供准确、有用的信息。",
                "prologue": "您好！我是经略销售助手，很高兴为您服务。我可以帮您：\n• 分析产品图片\n• 回答产品相关问题\n• 提供销售建议\n\n请告诉我您需要什么帮助？",
                "quote": True,
                "empty_response": "抱歉，我在知识库中没有找到相关信息。请您提供更多详细信息，或者联系我们的销售团队获取帮助。",
                "parameters": []
            },
            "top_n": 6,
            "top_k": 1024,
            "similarity_threshold": 0.2,
            "vector_similarity_weight": 0.3,
            "rerank_id": "",
            "icon": "",
            "status": "1"
        }

        if DialogService.save(**default_dialog):
            logging.info(f"Default dialog '经略销售助手' created successfully with ID: {default_dialog['id']}")
            return default_dialog['id']
        else:
            logging.error("Failed to create default dialog.")
            return None
    except Exception as e:
        logging.error(f"Error creating default dialog: {e}")
        return None


def get_default_dialog_id():
    """获取默认助手ID"""
    from api.db.services.dialog_service import DialogService

    try:
        # 查找名为"经略销售助手"的助手
        dialogs = DialogService.query(name="经略销售助手")
        if dialogs:
            return dialogs[0].id
        else:
            logging.warning("Default dialog '经略销售助手' not found.")
            return None
    except Exception as e:
        logging.error(f"Error getting default dialog ID: {e}")
        return None


def init_superuser():
    # 使用您提供的管理员信息
    admin_phone = "18502312097"
    admin_name = "罗莉"

    user_info = {
        "id": uuid.uuid1().hex,
        "nickname": admin_name,
        "is_superuser": True,  # 只使用is_superuser字段
        "phone": admin_phone,
        "email": f"{admin_phone}@admin.local",  # 设置临时邮箱，满足数据库约束
        "creator": "system",
        "status": "1",
    }
    tenant = {
        "id": user_info["id"],
        "name": user_info["nickname"] + "‘s Kingdom",
        "llm_id": settings.CHAT_MDL,
        "embd_id": settings.EMBEDDING_MDL,
        "asr_id": settings.ASR_MDL,
        "parser_ids": settings.PARSERS,
        "img2txt_id": settings.IMAGE2TEXT_MDL
    }
    usr_tenant = {
        "tenant_id": user_info["id"],
        "user_id": user_info["id"],
        "invited_by": user_info["id"],
        "role": UserTenantRole.OWNER
    }
    tenant_llm = []
    for llm in LLMService.query(fid=settings.LLM_FACTORY):
        tenant_llm.append(
            {"tenant_id": user_info["id"], "llm_factory": settings.LLM_FACTORY, "llm_name": llm.llm_name,
             "model_type": llm.model_type,
             "api_key": settings.API_KEY, "api_base": settings.LLM_BASE_URL})

    if not UserService.save(**user_info):
        logging.error("can't init admin.")
        return
    TenantService.insert(**tenant)
    UserTenantService.insert(**usr_tenant)
    TenantLLMService.insert_many(tenant_llm)
    logging.info(
        f"Super user initialized. phone: {admin_phone}, name: {admin_name}. Login via SMS verification code only.")

    # 跳过LLM测试，避免因为API配置问题导致初始化失败
    logging.info("Skipping LLM tests during initialization to avoid API configuration issues.")

    # 创建默认知识库
    kb_id = create_default_knowledgebase(user_info["id"], t.embd_id)

    # 创建默认助手
    if kb_id:
        create_default_dialog(user_info["id"], kb_id)


def init_llm_factory():
    try:
        LLMService.filter_delete([(LLM.fid == "MiniMax" or LLM.fid == "Minimax")])
        LLMService.filter_delete([(LLM.fid == "cohere")])
        LLMFactoriesService.filter_delete([LLMFactories.name == "cohere"])
    except Exception:
        pass

    factory_llm_infos = settings.FACTORY_LLM_INFOS    
    for factory_llm_info in factory_llm_infos:
        info = deepcopy(factory_llm_info)
        llm_infos = info.pop("llm")
        try:
            LLMFactoriesService.save(**info)
        except Exception:
            pass
        LLMService.filter_delete([LLM.fid == factory_llm_info["name"]])
        for llm_info in llm_infos:
            llm_info["fid"] = factory_llm_info["name"]
            try:
                LLMService.save(**llm_info)
            except Exception:
                pass

    LLMFactoriesService.filter_delete([(LLMFactories.name == "Local") | (LLMFactories.name == "novita.ai")])
    LLMService.filter_delete([LLM.fid == "Local"])
    LLMService.filter_delete([LLM.llm_name == "qwen-vl-max"])
    LLMService.filter_delete([LLM.fid == "Moonshot", LLM.llm_name == "flag-embedding"])
    TenantLLMService.filter_delete([TenantLLM.llm_factory == "Moonshot", TenantLLM.llm_name == "flag-embedding"])
    LLMFactoriesService.filter_delete([LLMFactoriesService.model.name == "QAnything"])
    LLMService.filter_delete([LLMService.model.fid == "QAnything"])
    TenantLLMService.filter_update([TenantLLMService.model.llm_factory == "QAnything"], {"llm_factory": "Youdao"})
    TenantLLMService.filter_update([TenantLLMService.model.llm_factory == "cohere"], {"llm_factory": "Cohere"})
    TenantService.filter_update([1 == 1], {
        "parser_ids": "naive:General,qa:Q&A,resume:Resume,manual:Manual,table:Table,paper:Paper,book:Book,laws:Laws,presentation:Presentation,picture:Picture,one:One,audio:Audio,email:Email,tag:Tag"})
    ## insert openai two embedding models to the current openai user.
    # print("Start to insert 2 OpenAI embedding models...")
    tenant_ids = set([row["tenant_id"] for row in TenantLLMService.get_openai_models()])
    for tid in tenant_ids:
        for row in TenantLLMService.query(llm_factory="OpenAI", tenant_id=tid):
            row = row.to_dict()
            row["model_type"] = LLMType.EMBEDDING.value
            row["llm_name"] = "text-embedding-3-small"
            row["used_tokens"] = 0
            try:
                TenantLLMService.save(**row)
                row = deepcopy(row)
                row["llm_name"] = "text-embedding-3-large"
                TenantLLMService.save(**row)
            except Exception:
                pass
            break
    for kb_id in KnowledgebaseService.get_all_ids():
        KnowledgebaseService.update_document_number_in_init(kb_id=kb_id, doc_num=DocumentService.get_kb_doc_count(kb_id))



def add_graph_templates():
    dir = os.path.join(get_project_base_directory(), "agent", "templates")
    for fnm in os.listdir(dir):
        try:
            cnvs = json.load(open(os.path.join(dir, fnm), "r",encoding="utf-8"))
            try:
                CanvasTemplateService.save(**cnvs)
            except Exception:
                CanvasTemplateService.update_by_id(cnvs["id"], cnvs)
        except Exception:
            logging.exception("Add graph templates error: ")


def init_web_data():
    start_time = time.time()

    init_llm_factory()
    if not UserService.get_all().count():
        init_superuser()

    add_graph_templates()
    logging.info("init web data success:{}".format(time.time() - start_time))


if __name__ == '__main__':
    init_web_db()
    init_web_data()
