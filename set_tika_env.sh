#!/bin/bash

# RAGFlow Tika环境变量配置脚本
# 用于固化Tika配置，避免重启后丢失

echo "设置Tika环境变量..."

# 设置Tika服务器端点
export TIKA_SERVER_ENDPOINT="http://localhost:9998"
export TIKA_CLIENT_ONLY=true

# 将环境变量写入.bashrc以持久化
if ! grep -q "TIKA_SERVER_ENDPOINT" ~/.bashrc; then
    echo "" >> ~/.bashrc
    echo "# RAGFlow Tika配置" >> ~/.bashrc
    echo "export TIKA_SERVER_ENDPOINT=\"http://localhost:9998\"" >> ~/.bashrc
    echo "export TIKA_CLIENT_ONLY=true" >> ~/.bashrc
    echo "Tika环境变量已添加到 ~/.bashrc"
else
    echo "Tika环境变量已存在于 ~/.bashrc"
fi

# 检查Docker Tika服务状态
if docker ps | grep -q "ragflow-tika"; then
    echo "✅ Docker Tika服务正在运行"
else
    echo "🚀 启动Docker Tika服务..."
    docker run -d --name ragflow-tika -p 9998:9998 apache/tika:latest
    sleep 5
    
    # 测试Tika服务
    if curl -s http://localhost:9998/version > /dev/null; then
        echo "✅ Docker Tika服务启动成功"
    else
        echo "❌ Docker Tika服务启动失败"
    fi
fi

echo "当前Tika配置:"
echo "  TIKA_SERVER_ENDPOINT: $TIKA_SERVER_ENDPOINT"
echo "  TIKA_CLIENT_ONLY: $TIKA_CLIENT_ONLY"

# 测试Tika连接
echo "测试Tika连接..."
if curl -s http://localhost:9998/version; then
    echo ""
    echo "✅ Tika服务连接正常"
else
    echo "❌ Tika服务连接失败"
fi
