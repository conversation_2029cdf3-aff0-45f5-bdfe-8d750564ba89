# RAGFlow Nginx配置
# 将此配置添加到您现有的Nginx配置中

upstream ragflow_backend {
    server 127.0.0.1:8080;
}

server {
    listen 80;
    server_name ragflow.yourdomain.com;  # 请替换为您的域名
    
    # 如果您有SSL证书，可以添加HTTPS配置
    # listen 443 ssl;
    # ssl_certificate /path/to/your/cert.pem;
    # ssl_certificate_key /path/to/your/key.pem;
    
    client_max_body_size 1G;  # 允许上传大文件
    
    # RAGFlow主应用
    location / {
        proxy_pass http://ragflow_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # API路径
    location /api/ {
        proxy_pass http://ragflow_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 增加API超时时间
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://ragflow_backend;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 日志配置
    access_log /var/log/nginx/ragflow_access.log;
    error_log /var/log/nginx/ragflow_error.log;
}

# MinIO管理界面（可选）
server {
    listen 80;
    server_name minio.yourdomain.com;  # 请替换为您的域名
    
    location / {
        proxy_pass http://127.0.0.1:9001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
